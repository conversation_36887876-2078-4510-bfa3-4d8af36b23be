# EIP挂载和卸载API接口使用指南

## 概述

本文档介绍了新增的EIP挂载和卸载API接口的使用方法。这些接口允许您独立地挂载和卸载EIP，而不需要重新创建整个EIP基础设施。

## API接口

### 1. 挂载EIP

**接口地址：** `POST /network/eip/mount`

**功能：** 挂载EIP到指定的VLAN，只创建nft规则，不创建基础设施

**请求参数：**
```json
{
    "tenant_id": "string",      // 租户ID
    "vlan_id": 100,            // VLAN ID
    "eip": "************",     // 外部IP地址
    "gateway_ip": "***********", // 网关IP地址
    "internal_ip": "**************" // 内部IP地址
}
```

**响应示例：**
```json
{
    "success": true,
    "instruction_id": "7c956a1ff4c81da1f3fa8410a1225d69ea229f56",
    "message": "EIP mount request submitted"
}
```

**curl示例：**
```bash
curl -X POST "http://localhost:8080/network/eip/mount" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************",
       "gateway_ip": "***********",
       "internal_ip": "**************"
     }'
```

### 2. 卸载EIP

**接口地址：** `DELETE /network/eip/mount`

**功能：** 卸载指定的EIP，只删除nft规则，不删除基础设施

**请求参数：**
```json
{
    "tenant_id": "string",      // 租户ID
    "vlan_id": 100,            // VLAN ID
    "eip": "************"      // 外部IP地址
}
```

**响应示例：**
```json
{
    "success": true,
    "instruction_id": "7c956a1ff4c81da1f3fa8410a1225d69ea229f56",
    "message": "EIP unmount request submitted"
}
```

**curl示例：**
```bash
curl -X DELETE "http://localhost:8080/network/eip/mount" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************"
     }'
```

## 使用场景

### 场景1：动态EIP管理

当您需要为同一个VLAN动态添加或移除多个EIP时：

```bash
# 1. 首先创建子网网关（建立基础设施）
curl -X POST "http://localhost:8080/network/subnet-gateway" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "subnet_gw_ip": "*************"
     }'

# 2. 创建EIP基础设施
curl -X POST "http://localhost:8080/network/eip" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************",
       "gateway_ip": "***********",
       "internal_ip": "**************"
     }'

# 3. 挂载额外的EIP（只创建规则）
curl -X POST "http://localhost:8080/network/eip/mount" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************",
       "gateway_ip": "***********",
       "internal_ip": "**************"
     }'

# 4. 卸载特定EIP（只删除规则）
curl -X DELETE "http://localhost:8080/network/eip/mount" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************"
     }'
```

### 场景2：EIP故障转移

当需要快速切换EIP时：

```bash
# 卸载故障EIP
curl -X DELETE "http://localhost:8080/network/eip/mount" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************"
     }'

# 挂载备用EIP
curl -X POST "http://localhost:8080/network/eip/mount" \
     -H "Content-Type: application/json" \
     -d '{
       "tenant_id": "tenant_123",
       "vlan_id": 100,
       "eip": "************",
       "gateway_ip": "***********",
       "internal_ip": "**************"
     }'
```

## 错误处理

### 常见错误码

- **400 Bad Request**: 请求参数无效或缺少必需字段
- **422 Unprocessable Entity**: 请求格式正确但参数验证失败
- **500 Internal Server Error**: 服务器内部错误
- **503 Service Unavailable**: 节点不健康

### 错误响应示例

```json
{
    "detail": "EIP, gateway IP and internal IP are required"
}
```

## 指令状态查询

挂载和卸载操作都是异步的，您可以通过指令ID查询执行状态：

```bash
curl "http://localhost:8080/instruction/{instruction_id}?tenant_id=tenant_123"
```

**响应示例：**
```json
{
    "success": true,
    "instruction": {
        "id": "7c956a1ff4c81da1f3fa8410a1225d69ea229f56",
        "type": "eip_mount",
        "tenant_id": "tenant_123",
        "vlan_id": 100,
        "eip": "************",
        "gateway_ip": "***********",
        "internal_ip": "**************",
        "shard_id": "shard_1",
        "cmds": [...],
        "revocation": [...]
    }
}
```

## 与现有API的区别

| 操作 | 现有API | 新增API | 区别 |
|------|---------|---------|------|
| 创建EIP | `POST /network/eip` | `POST /network/eip/mount` | 现有API创建完整基础设施，新API只创建规则 |
| 删除EIP | `DELETE /network/eip` | `DELETE /network/eip/mount` | 现有API删除完整基础设施，新API只删除规则 |

## 最佳实践

1. **先创建基础设施**：在使用挂载API之前，确保已经创建了子网网关和EIP基础设施
2. **批量操作**：对于多个EIP的场景，使用挂载API可以提高效率
3. **错误处理**：始终检查响应状态码和错误信息
4. **状态监控**：使用指令状态查询API监控操作执行情况
5. **资源清理**：在删除子网网关之前，确保已卸载所有挂载的EIP

## 技术细节

### 生成的命令示例

**挂载EIP时生成的命令：**
```bash
# 配置EIP地址
ip netns exec ns-vlan100 ip addr add ************/24 dev v-eip-ns-100

# 添加DNAT规则
ip netns exec ns-vlan100 nft add rule ip nat PREROUTING ip daddr ************ counter dnat to **************

# 添加SNAT规则
ip netns exec ns-vlan100 nft add rule ip nat POSTROUTING ip saddr ************** ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } snat to ************

# 显示当前规则
ip netns exec ns-vlan100 nft list table ip nat
```

**卸载EIP时生成的命令：**
```bash
# 删除EIP地址
ip netns exec ns-vlan100 ip addr del ************/24 dev v-eip-ns-100 || true

# 删除对应的nft规则
ip netns exec ns-vlan100 nft delete rule ip nat PREROUTING ip daddr ************ counter dnat to ************** || true
ip netns exec ns-vlan100 nft delete rule ip nat POSTROUTING ip saddr ************** ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } snat to ************ || true

# 显示剩余规则
ip netns exec ns-vlan100 nft list table ip nat
```

## 注意事项

1. **依赖关系**：EIP挂载依赖于已存在的子网网关和nft基础设施
2. **并发安全**：API支持并发操作，但建议避免对同一EIP的并发挂载/卸载操作
3. **资源限制**：每个VLAN可以挂载的EIP数量受系统资源限制
4. **网络配置**：确保网关IP和内部IP配置正确，避免网络冲突
