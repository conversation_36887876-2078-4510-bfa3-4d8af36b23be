"""
Agent management for v-switch core service.
"""

import json
import time
import logging
from typing import Dict, Any, List, Optional
from etcd3.events import PutEvent, DeleteEvent
from ..common.etcd_client import ETCDClient
from ..config.server_config import ServerConfig


class AgentManager:
    """管理agent注册和分片分配。"""
    
    METADATA_KEY = "/network/server/metadata"
    REGISTER_PREFIX = "/network/agent/register/"
    INSTANCE_PREFIX = "/network/agent/instance/"
    
    def __init__(self, etcd_client: ETCDClient, config: ServerConfig):
        """初始化agent管理器。
        
        Args:
            etcd_client: ETCD client instance
            config: Server configuration
        """
        self.etcd_client = etcd_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._metadata_cache = None
        
    def initialize_metadata(self) -> bool:
        """在etcd中初始化服务器元数据。
        
        Returns:
            True if successful
        """
        try:
            metadata = {
                "server": {
                    "shard_count": self.config.server.shard_count
                },
                "agent": []
            }
            # 判断元数据是否已经存在
            existing_data, _ = self.etcd_client.get_json(self.METADATA_KEY)
            if existing_data:
                self.logger.info("Server metadata already exists, skipping initialization")
                self._metadata_cache = existing_data
                return True
            # 如果不存在, 则创建新的元数据
            success = self.etcd_client.put_json(self.METADATA_KEY, metadata)
            if success:
                self.logger.info("Initialized server metadata")
                self._metadata_cache = metadata
            else:
                self.logger.error("Failed to initialize server metadata")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error initializing metadata: {e}")
            return False

    def check_agent_status(self) -> bool:
        """检查所有agent的是否在线，是否有分配分片。
        Returns:
            True if all agents are healthy
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                self.logger.error("Failed to get metadata")
                return False
            
            # 获取在线的agent列表
            online_agents = self.get_online_agents(metadata)
            if not online_agents:
                self.logger.warning("No online agents found")
                return False
            
            # 检查每个agent的状态
            for agent in online_agents:
                agent_id = agent.get("agent_id")
                if not agent_id:
                    self.logger.error("Agent ID is missing in metadata")
                    return False
                # 检查分片是否分配
                if "shard" not in agent or not agent["shard"]:
                    self.logger.warning(f"Agent {agent_id} has no assigned shards")
                    return False
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking agent status: {e}")
            return False
        
    def check_agent_registration(self) -> bool:
        """检查所有agent的注册状态, 并更新元数据。
            # 启动时主动检查一次agent(可能在服务停止期间遗漏了事件的处理), 
            # 1. 查询metadata数据
            # 2. 查询注册的agent列表
            # 3. 比对metadata和注册列表，如果存在agent.online为False但metadata中仍有分配分片的需要重新分配
        Returns:
            True if successful
        """
        try:
            # 获取当前元数据
            metadata = self.get_metadata()
            if not metadata:
                return False
            
            # 获取所有注册的agent
            registered_agents = self.etcd_client.get_prefix(self.REGISTER_PREFIX)
            registered_agent_ids = {key.replace(self.REGISTER_PREFIX, "") for key, _ in registered_agents}
            
            # 检查每个agent的状态, 
            # 1.如果注册的agent且online为True但不在元数据中, 则添加到元数据
            # 2.如果元数据中有agent不在注册列表中或online为False, 则将其设置为离线, 并重新分配分片
            for agent in metadata.get("agent", []):
                agent_id = agent.get("agent_id")
                if agent_id in registered_agent_ids:
                    # 如果agent在线且在注册列表中, 则不需要处理
                    if agent.get("online", False):
                        continue
                else:
                    # 如果agent不在注册列表中, 则将其设置为离线
                    if agent.get("online", True):
                        self.handle_agent_offline(agent_id)
                    continue
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking agent status: {e}")
            return False
        
    def get_metadata(self) -> Optional[Dict[str, Any]]:
        """获取服务的元数据。
        
        Returns:
            Server metadata or None if not found
        """
        try:
            data, _ = self.etcd_client.get_json(self.METADATA_KEY)
            if data:
                self._metadata_cache = data
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting metadata: {e}")
            return None
    
    def update_metadata(self, metadata: Dict[str, Any]) -> bool:
        """更新服务的元数据。
        
        Args:
            metadata: Updated metadata
            
        Returns:
            True if successful
        """
        try:
            success = self.etcd_client.put_json(self.METADATA_KEY, metadata)
            if success:
                self._metadata_cache = metadata
                self.logger.debug("Updated server metadata")
            else:
                self.logger.error("Failed to update server metadata")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating metadata: {e}")
            return False
    
    def find_agent_in_metadata(self, agent_id: str, metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """在元数据中查找指定的 agent 信息。
        
        Args:
            agent_id: Agent identifier
            metadata: Server metadata
            
        Returns:
            Agent data or None if not found
        """
        for agent in metadata.get("agent", []):
            if agent.get("agent_id") == agent_id:
                return agent
        return None
    
    def get_online_agents(self, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取在线状态的 agent 列表。
        
        Args:
            metadata: Server metadata
            
        Returns:
            List of online agent data
        """
        return [agent for agent in metadata.get("agent", []) if agent.get("online", False)]
    
    def get_assigned_shards(self, metadata: Dict[str, Any]) -> List[int]:
        """获取所有已分配的分片。"""
        shards = set()
        for agent in metadata.get("agent", []):
            shards.update(agent.get("shard", []))
        return list(shards)

    def assign_shards(self, agent_id: str, metadata: Dict[str, Any]) -> bool:
        """为新上线的 agent 分配分片。
        
        Args:
            agent_id: Agent identifier
            metadata: Server metadata
            
        Returns:
            True if successful
        """
        try:

            # 获取总的分片数量
            shard_count = self.config.server.shard_count
            # 获取所有已分配的分片列表
            assigned_shards = self.get_assigned_shards(metadata)
            # 获取未分配的分片
            unassigned_shards = set()
            for shard in range(shard_count):
                if shard not in assigned_shards:
                    unassigned_shards.add(shard)
            # 如果没有未分配的分片, 则不需要分配
            if not unassigned_shards:
                self.logger.debug("No unassigned shards available")
                return True
            
            # 分配分片给新上线的 agent
            agent = self.find_agent_in_metadata(agent_id, metadata)
            if not agent:
                self.logger.error(f"Agent {agent_id} not found in metadata")
                return False
            if "shard" not in agent:
                agent["shard"] = []
            for shard in unassigned_shards:
                agent["shard"].append(shard)
                self.logger.info(f"Assigned shard {shard} to agent {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error assigning shards: {e}")
            return False

    def reassign_shards(self, offline_agent_id: str, metadata: Dict[str, Any]) -> bool:
        """把离线节点的分片重新分配给在线节点。
        
        Args:
            offline_agent_id: ID of offline agent
            metadata: Server metadata
            
        Returns:
            True if successful
        """
        try:
            # 获取离线节点信息
            offline_agent = self.find_agent_in_metadata(offline_agent_id, metadata)
            if not offline_agent or not offline_agent.get("shard"):
                return True  # 没有需要重新分配的分片
            
            # 获取需要重新分配的分片
            shards_to_reassign = offline_agent["shard"]
            offline_agent["shard"] = []
            
            # 获取在线节点列表
            online_agents = self.get_online_agents(metadata)
            if not online_agents:
                self.logger.warning("No online agents available for shard reassignment")
                return False
            
            # 将分片分配给在线节点
            for i, shard in enumerate(shards_to_reassign):
                target_agent = online_agents[i % len(online_agents)]
                if "shard" not in target_agent:
                    target_agent["shard"] = []
                target_agent["shard"].append(shard)
                
                self.logger.info(f"Reassigned shard {shard} from {offline_agent_id} to {target_agent['agent_id']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error reassigning shards: {e}")
            return False
    
    def handle_agent_offline(self, agent_id: str) -> bool:
        """处理 agent 离线事件。
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            True if successful
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                return False
            
            # 在 metadata 中查找 agent
            agent = self.find_agent_in_metadata(agent_id, metadata)
            if not agent:
                # 如果 agent 不在 metadata 中, 则添加一个离线的 agent
                agent = {
                    "online": False,
                    "agent_id": agent_id,
                    "offline_time": int(time.time()),
                    "shard": []
                }
                metadata["agent"].append(agent)
            else:
                # 将 agent 设置为离线状态
                agent["online"] = False
                agent["offline_time"] = int(time.time())
                
                # 如果 agent 有分片, 则重新分配分片
                if agent.get("shard"):
                    self.reassign_shards(agent_id, metadata)
            
            # 更新 metadata
            success = self.update_metadata(metadata)
            if success:
                self.logger.info(f"Agent {agent_id} marked as offline")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error handling agent offline {agent_id}: {e}")
            return False

    def handle_agent_online(self, agent_id: str) -> bool:
        """处理 agent 上线事件。

        Args:
            agent_id: Agent identifier

        Returns:
            True if successful
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                return False

            # 在 metadata 中查找 agent
            agent = self.find_agent_in_metadata(agent_id, metadata)
            if not agent:
                # 如果 agent 不在 metadata 中, 则添加一个在线的 agent
                agent = {
                    "online": True,
                    "agent_id": agent_id,
                    "offline_time": 0,
                    "shard": []
                }
                metadata["agent"].append(agent)
            elif agent.get("online") is False:
                # 将 agent 设置为在线状态
                agent["online"] = True
                agent["offline_time"] = 0
                # 分配分片给新上线的 agent
                if not self.assign_shards(agent_id, metadata):
                    return False
            else:
                # agent 已经在线, 则不需要处理
                self.logger.debug(f"Agent {agent_id} is already online")
                return True

            # 更新 metadata
            success = self.update_metadata(metadata)
            if success:
                self.logger.info(f"Agent {agent_id} marked as online")

            return success

        except Exception as e:
            self.logger.error(f"Error handling agent online {agent_id}: {e}")
            return False

    def start_agent_monitoring(self) -> None:
        """开始监控 agent 注册事件."""
        def handle_register_event(event):
            """处理 agent 注册事件."""
            try:

                key = event.key.decode('utf-8')
                agent_id = key.replace(self.REGISTER_PREFIX, "")

                if isinstance(event, DeleteEvent):
                    # 处理 agent 离线事件
                    
                    self.handle_agent_offline(agent_id)

                elif isinstance(event, PutEvent):
                    # 处理 agent 注册事件
                    if event.value:
                        try:
                            agent_data = json.loads(event.value.decode('utf-8'))
                            status = agent_data.get("status", "")
                            # 如果没有状态, 则默认为空字符串
                            prev_status = ""
                            # 获取之前的状态, 如果是新注册的 agent, prev_kv 会是 None
                            if hasattr(event, 'prev_kv') and event.prev_kv and event.prev_kv.value:
                                prev_data = json.loads(event.prev_kv.value.decode('utf-8'))
                                prev_status = prev_data.get("status", "")

                            if prev_status == "running" and status == "error":
                                # running -> error: 处理为离线事件
                                self.handle_agent_offline(agent_id)
                            elif prev_status in ["error", ""] and status == "running":
                                # error -> running or new -> running: 处理为上线事件
                                self.handle_agent_online(agent_id)
                            # running -> running: 不需要处理

                        except json.JSONDecodeError as e:
                            self.logger.error(f"Invalid JSON in agent registration {agent_id}: {e}")

            except Exception as e:
                self.logger.error(f"Error handling agent registration event: {e}")

        # 开始监控 agent 注册前缀
        self.logger.info("Starting agent registration monitoring")
        self.etcd_client.watch_prefix(self.REGISTER_PREFIX, handle_register_event)
