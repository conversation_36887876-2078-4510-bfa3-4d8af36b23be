"""
IP分配器 - IP address allocator for v-switch core service.

This module provides IP address allocation and management functionality
with ETCD-based persistence and coordination.
"""

import json
import logging
import ipaddress
from typing import Dict, Any, List, Optional, Set
from ..common.etcd_client import ETCDClient
from ..utils.validation_utils import ValidationUtils


class IPAllocatorError(Exception):
    """IP分配器相关错误的基类。"""
    pass


class IPRangeExhaustedError(IPAllocatorError):
    """IP地址范围已耗尽错误。"""
    pass


class InvalidIPError(IPAllocatorError):
    """无效IP地址错误。"""
    pass


class IPAllocator:
    """IP地址分配器。
    
    提供IP地址的分配、释放和管理功能，使用ETCD进行数据持久化。
    支持IP地址回收和重用机制。
    """
    
    def __init__(self, ip_min: str, ip_max: str, etcd_client: ETCDClient, key: str):
        """初始化IP分配器实例。
        
        Args:
            ip_min: 最小IP地址
            ip_max: 最大IP地址  
            etcd_client: etcd客户端实例
            key: etcd中存储IP地址的key（不带前缀）
            
        Raises:
            InvalidIPError: 当IP地址格式无效时
            IPAllocatorError: 当初始化失败时
        """
        self.logger = logging.getLogger(__name__)
        
        # 验证IP地址格式
        if not ValidationUtils.validate_ip_address(ip_min):
            raise InvalidIPError(f"Invalid minimum IP address: {ip_min}")
        if not ValidationUtils.validate_ip_address(ip_max):
            raise InvalidIPError(f"Invalid maximum IP address: {ip_max}")
            
        # 转换为ipaddress对象进行范围验证
        try:
            min_ip = ipaddress.ip_address(ip_min)
            max_ip = ipaddress.ip_address(ip_max)
            if min_ip >= max_ip:
                raise InvalidIPError(f"Minimum IP {ip_min} must be less than maximum IP {ip_max}")
        except ValueError as e:
            raise InvalidIPError(f"Invalid IP address range: {e}")
            
        self.ip_min = ip_min
        self.ip_max = ip_max
        self.etcd_client = etcd_client
        self.key = key
        
        # 内存中的状态缓存
        self.allocated_ips: Set[str] = set()
        self.reclaimed_ips: List[str] = []
        self.next_ip: Optional[str] = None
        
        # 初始化分配器数据
        self._initialize_data()
    
    def _initialize_data(self) -> None:
        """从etcd中获取IP分配器数据，如果没有则创建空数据。
        
        Raises:
            IPAllocatorError: 当初始化失败时
        """
        try:
            # 从etcd获取现有数据
            data, _ = self.etcd_client.get_json(self.key)
            
            if data is None:
                # 创建新的分配器数据
                self.logger.info(f"Creating new IP allocator data for key: {self.key}")
                data = {
                    "ip_min": self.ip_min,
                    "ip_max": self.ip_max,
                    "next_ip": self.ip_min,
                    "allocated_ips": [],
                    "reclaimed_ips": []
                }
                
                # 保存到etcd
                if not self.etcd_client.put_json(self.key, data):
                    raise IPAllocatorError("Failed to initialize IP allocator data in ETCD")
            else:
                self.logger.info(f"Loaded existing IP allocator data for key: {self.key}")
                
            # 加载数据到内存
            self.allocated_ips = set(data.get("allocated_ips", []))
            self.reclaimed_ips = list(data.get("reclaimed_ips", []))
            self.next_ip = data.get("next_ip", self.ip_min)
            
            self.logger.debug(f"Initialized with {len(self.allocated_ips)} allocated IPs, "
                            f"{len(self.reclaimed_ips)} reclaimed IPs")
                            
        except Exception as e:
            raise IPAllocatorError(f"Failed to initialize IP allocator: {e}")
    
    def allocate_ip(self) -> str:
        """分配一个IP地址。
        
        执行分配逻辑：
        1. 检查回收的IP地址列表，如果有则优先使用
        2. 如果回收列表为空，从next_ip开始查找可用IP
        3. 跳过已分配的IP地址，直到找到可用IP或超出范围
        4. 更新etcd中的分配器数据
        
        Returns:
            分配的IP地址
            
        Raises:
            IPRangeExhaustedError: 当IP地址范围已耗尽时
            IPAllocatorError: 当分配失败时
        """
        try:
            allocated_ip = None
            
            # 1. 优先从回收列表中分配
            if self.reclaimed_ips:
                allocated_ip = self.reclaimed_ips.pop(0)
                self.logger.debug(f"Allocated IP from reclaimed list: {allocated_ip}")
            else:
                # 2. 从next_ip开始查找可用IP
                allocated_ip = self._find_next_available_ip()
                
            # 3. 添加到已分配列表
            self.allocated_ips.add(allocated_ip)
            
            # 4. 更新etcd数据
            self._update_etcd_data()
            
            self.logger.info(f"Successfully allocated IP: {allocated_ip}")
            return allocated_ip
            
        except Exception as e:
            if isinstance(e, (IPRangeExhaustedError, IPAllocatorError)):
                raise
            raise IPAllocatorError(f"Failed to allocate IP: {e}")
    
    def _find_next_available_ip(self) -> str:
        """从next_ip开始查找下一个可用的IP地址。
        
        Returns:
            可用的IP地址
            
        Raises:
            IPRangeExhaustedError: 当IP地址范围已耗尽时
        """
        current_ip = ipaddress.ip_address(self.next_ip)
        max_ip = ipaddress.ip_address(self.ip_max)
        
        while current_ip <= max_ip:
            current_ip_str = str(current_ip)
            
            # 检查IP是否已分配
            if current_ip_str not in self.allocated_ips:
                # 更新next_ip为下一个IP
                self.next_ip = str(current_ip + 1) if current_ip < max_ip else self.ip_max
                self.logger.debug(f"Found available IP: {current_ip_str}, next_ip updated to: {self.next_ip}")
                return current_ip_str
                
            current_ip += 1
            
        # 如果到这里说明IP范围已耗尽
        raise IPRangeExhaustedError(f"IP address range {self.ip_min}-{self.ip_max} is exhausted")
    
    def release_ip(self, ip: str) -> bool:
        """释放一个IP地址。
        
        执行释放逻辑：
        1. 检查要释放的IP是否在已分配列表中
        2. 从已分配列表中删除
        3. 添加到回收列表中
        4. 更新etcd数据
        
        Args:
            ip: 要释放的IP地址
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # 验证IP地址格式
            if not ValidationUtils.validate_ip_address(ip):
                self.logger.error(f"Invalid IP address format: {ip}")
                return False
                
            # 检查IP是否在分配范围内
            if not self._is_ip_in_range(ip):
                self.logger.error(f"IP {ip} is not in the allocation range {self.ip_min}-{self.ip_max}")
                return False
                
            # 检查IP是否已分配
            if ip not in self.allocated_ips:
                self.logger.warning(f"IP {ip} is not in allocated list, cannot release")
                return False
                
            # 从已分配列表中删除
            self.allocated_ips.remove(ip)
            
            # 添加到回收列表（如果不在其中）
            if ip not in self.reclaimed_ips:
                self.reclaimed_ips.append(ip)
                
            # 更新etcd数据
            self._update_etcd_data()
            
            self.logger.info(f"Successfully released IP: {ip}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to release IP {ip}: {e}")
            return False
    
    def _is_ip_in_range(self, ip: str) -> bool:
        """检查IP地址是否在分配范围内。
        
        Args:
            ip: IP地址
            
        Returns:
            True if IP is in range
        """
        try:
            ip_addr = ipaddress.ip_address(ip)
            min_ip = ipaddress.ip_address(self.ip_min)
            max_ip = ipaddress.ip_address(self.ip_max)
            return min_ip <= ip_addr <= max_ip
        except ValueError:
            return False
    
    def _update_etcd_data(self) -> None:
        """更新etcd中的IP分配器数据。
        
        Raises:
            IPAllocatorError: 当更新失败时
        """
        try:
            data = {
                "ip_min": self.ip_min,
                "ip_max": self.ip_max,
                "next_ip": self.next_ip,
                "allocated_ips": list(self.allocated_ips),
                "reclaimed_ips": self.reclaimed_ips
            }
            
            if not self.etcd_client.put_json(self.key, data):
                raise IPAllocatorError("Failed to update IP allocator data in ETCD")
                
            self.logger.debug("Successfully updated ETCD data")
            
        except Exception as e:
            raise IPAllocatorError(f"Failed to update ETCD data: {e}")
    
    def get_allocation_status(self) -> Dict[str, Any]:
        """获取IP分配状态信息。
        
        Returns:
            包含分配状态的字典
        """
        try:
            min_ip = ipaddress.ip_address(self.ip_min)
            max_ip = ipaddress.ip_address(self.ip_max)
            total_ips = int(max_ip) - int(min_ip) + 1
            
            return {
                "ip_range": f"{self.ip_min}-{self.ip_max}",
                "total_ips": total_ips,
                "allocated_count": len(self.allocated_ips),
                "reclaimed_count": len(self.reclaimed_ips),
                "available_count": total_ips - len(self.allocated_ips),
                "next_ip": self.next_ip,
                "utilization_rate": len(self.allocated_ips) / total_ips * 100
            }
        except Exception as e:
            self.logger.error(f"Failed to get allocation status: {e}")
            return {}
    
    def is_ip_allocated(self, ip: str) -> bool:
        """检查IP地址是否已分配。
        
        Args:
            ip: IP地址
            
        Returns:
            True if IP is allocated
        """
        return ip in self.allocated_ips
    
    def get_allocated_ips(self) -> List[str]:
        """获取所有已分配的IP地址列表。
        
        Returns:
            已分配IP地址列表
        """
        return list(self.allocated_ips)
    
    def get_reclaimed_ips(self) -> List[str]:
        """获取所有已回收的IP地址列表。
        
        Returns:
            已回收IP地址列表
        """
        return self.reclaimed_ips.copy()

