"""
分片管理器为v-switch核心服务提供分片分配工作和指令存储。
"""

import logging
import hashlib
import uhashring
from typing import List, Dict, Any, Optional
from ..common.etcd_client import ETCDClient
from ..config.server_config import ServerConfig

class InstrunctionData:
    """指令数据模型，用于存储指令相关信息。"""
    
    def __init__(self, tenant_id: str, shard_id: str, request_id: str, cmds: List[str]):
        self.tenant_id = tenant_id
        self.shard_id = shard_id
        self.request_id = request_id
        self.cmds = cmds

class ShardManager:
    """管理ETCD碎片分配工作和指令存储。"""
    
    ring: uhashring.HashRing

    def __init__(self, etcd_client: ETCDClient, config: ServerConfig):
        """初始化碎片管理器。
        
        Args:
            etcd_client: ETCD client instance
            config: Server configuration
        """
        self.etcd_client = etcd_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
    def initialize_shards(self) -> bool:
        """
        
        Returns:
            True if successful
        """
        try:

            self.ring = self.create_shard_ring()
            self.logger.info(f"Initialized shards: {self.nodes}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize shards: {e}")
            return False
        
    def create_shard_ring(self) -> uhashring.HashRing:
      """
      创建一个包含指定数量分片的一致性哈希环。
      分片节点被命名为 '0', '1', ...
      """
      shard_count: int = self.config.get_shard_count()
      # shard_count: int = 32
      self.nodes = [f'{i}' for i in range(shard_count)]
      return uhashring.HashRing(nodes=self.nodes, hash_fn=lambda k: hashlib.sha256(k.encode('utf-8')).digest())

    def get_shard_name_consistent(self, tenant_id: str, ring: uhashring.HashRing) -> str:
      """
      使用一致性哈希环为 tenant_id 获取其所属的分片名称。

      Args:
          tenant_id: 租户的唯一标识符。
          ring: 一个 uhashring.HashRing 实例。

      Returns:
          分片的名称 (例如 'shard-5')。
      """
      return ring.get_node(tenant_id)

    def get_shard_for_tenant(self, tenant_id: str) -> str:
        """为租户获取分片标识符。
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Shard identifier (e.g., '1', '2', ...)
        """
        # return self.config.get_shard_for_tenant(tenant_id)
        if not self.ring:
          self.ring = self.create_shard_ring()
        return self.get_shard_name_consistent(tenant_id, self.ring)
    
    def get_shard_path(self, shard_id: str) -> str:
        """为分片获取完整的ETCD路径。
        
        Args:
            shard_id: Shard identifier (e.g., 's1')
            
        Returns:
            Full ETCD path for the shard
        """
        return f"/network/server/instruct/{shard_id}"
    
    def get_instruction_key(self, shard_id: str, instruction_id: str) -> str:
        """为指令获取ETCD密钥。
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            
        Returns:
            Full ETCD key for the instruction
        """
        return f"{self.get_shard_path(shard_id)}/{instruction_id}"
    
    def put_instruction(self, shard_id: str, instruction_id: str, 
                       instruction_data: Dict[str, Any]) -> bool:
        """将指令放入分片。
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            instruction_data: Instruction data
            
        Returns:
            True if successful
        """
        try:
            key = self.get_instruction_key(shard_id, instruction_id)
            success = self.etcd_client.put_json(key, instruction_data)
            
            if success:
                self.logger.info(f"Put instruction {instruction_id} to shard {shard_id}")
            else:
                self.logger.error(f"Failed to put instruction {instruction_id} to shard {shard_id}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error putting instruction {instruction_id} to shard {shard_id}: {e}")
            return False
    
    def get_instruction(self, shard_id: str, instruction_id: str) -> Optional[Dict[str, Any]]:
        """从分片中获取指令。
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            
        Returns:
            Instruction data or None if not found
        """
        try:
            key = self.get_instruction_key(shard_id, instruction_id)
            data, _ = self.etcd_client.get_json(key)
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting instruction {instruction_id} from shard {shard_id}: {e}")
            return None
        
    # def get_instructions(self, shard_id: str) -> Dict[str, Dict[str, Any]]:
    #     """从分片中获取所有指令。
        
    #     Args:
    #         shard_id: Shard identifier
            
    #     Returns:
    #         Dictionary of instruction IDs and their data
    #     """
    #     try:
    #         shard_path = self.get_shard_path(shard_id)
    #         instructions = {}
            
    #         for key, value in self.etcd_client.get_prefix(shard_path):
    #             # Extract instruction ID from key
                
    #             if key.startswith(shard_path + "/"):
    #                 instruction_id = key[len(shard_path):]
    #                 instructions[instruction_id] = value
            
    #         return instructions
            
    #     except Exception as e:
    #         self.logger.error(f"Error getting instructions from shard {shard_id}: {e}")
    #         return {}
        
    def delete_instruction(self, shard_id: str, instruction_id: str) -> bool:
        """从分片中删除指令。
        
        Args:
            shard_id: Shard identifier
            instruction_id: Instruction identifier
            
        Returns:
            True if successful
        """
        try:
            key = self.get_instruction_key(shard_id, instruction_id)
            success = self.etcd_client.delete(key)
            
            if success:
                self.logger.info(f"Deleted instruction {instruction_id} from shard {shard_id}")
            else:
                self.logger.error(f"Failed to delete instruction {instruction_id} from shard {shard_id}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error deleting instruction {instruction_id} from shard {shard_id}: {e}")
            return False
    
    # def list_shard_instructions(self, shard_id: str) -> List[str]:
    #     """列出分片中的所有指令ID。
        
    #     Args:
    #         shard_id: Shard identifier
            
    #     Returns:
    #         List of instruction IDs
    #     """
    #     try:
    #         shard_path = self.get_shard_path(shard_id)
    #         instruction_ids = []
            
    #         for key, _ in self.etcd_client.get_prefix(shard_path):
    #             # Extract instruction ID from key
    #             if key.startswith(shard_path + "/"):
    #                 instruction_id = key[len(shard_path):]
    #                 instruction_ids.append(instruction_id)
            
    #         return instruction_ids
            
    #     except Exception as e:
    #         self.logger.error(f"Error listing instructions in shard {shard_id}: {e}")
    #         return []