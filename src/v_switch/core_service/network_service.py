"""
Network service for creating subnet gateways and EIPs.
"""

import hashlib
import logging
from typing import Dict, Any, List, Optional
from v_switch.common.etcd_client import ETCDClient
from v_switch.config.server_config import ServerConfig
from v_switch.core_service.shard_manager import ShardManager
from v_switch.core_service.ip_allocator import IPAllocator


class NetworkService:
    """处理网络配置请求。"""
    
    def __init__(self, etcd_client: ETCDClient, config: ServerConfig, shard_manager: ShardManager, mon_ip_allocator: IPAllocator):
        """初始化网络服务。
        
        Args:
            etcd_client: ETCD client instance
            config: Server configuration
            shard_manager: Shard manager instance
            mon_ip_allocator: IPAllocator for monnet
        """
        self.etcd_client = etcd_client
        self.config = config
        self.shard_manager = shard_manager
        self.mon_ip_allocator = mon_ip_allocator
        
        self.logger = logging.getLogger(__name__)

    def _generate_instruction_id(self, *params: str) -> str:
        """生成基于参数的 SHA-1 全局唯一 ID。

        Args:
            *params: 用于生成 ID 的参数列表

        Returns:
            SHA-1 哈希值作为指令 ID
        """
        # 使用 "|" 分割参数
        combined_params = "|".join(params)
        # 计算 SHA-1 哈希
        sha1_hash = hashlib.sha1(combined_params.encode('utf-8')).hexdigest()
        return sha1_hash
    
    def create_test_instruction(self, tenant_id: str, test_id: int) -> Optional[str]:
        """创建测试指令。

        Args:
            tenant_id: Tenant identifier
            test_id: Test identifier

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:
            # Generate unique instruction ID based on test_id
            instruction_id = self._generate_instruction_id("test", str(test_id))

            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)

            # 检查指令是否已经存在
            existing_instruction = self.shard_manager.get_instruction(shard_id, instruction_id)
            if existing_instruction:
                self.logger.info(f"Test instruction {instruction_id} already exists for tenant {tenant_id}")
                return instruction_id
            # Generate commands from template
            cmds = self._generate_test_commands(test_id)
            revocation = self._generate_test_revocation(test_id)

            # Create instruction
            instruction = {
                "id": instruction_id,
                "type": "test",
                "tenant_id": tenant_id,
                "test_id": test_id,
                "shard_id": shard_id,
                "cmds": cmds,
                "revocation": revocation
            }

            # Put instruction to shard using instruction_id as key
            success = self.shard_manager.put_instruction(shard_id, instruction_id, instruction)

            if success:
                self.logger.info(f"Created test instruction {instruction_id} for tenant {tenant_id}")
                return instruction_id
            else:
                self.logger.error(f"Failed to create test instruction for tenant {tenant_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating test for tenant {tenant_id}: {e}")
            return None

    def get_test_instruction_status(self, test_id: int, tenant_id: str) -> Optional[Dict[str, Any]]:
        """根据 test_id 获取测试指令状态。

        Args:
            test_id: Test identifier
            tenant_id: Tenant identifier

        Returns:
            Instruction data or None if not found
        """
        instruction_id = self._generate_instruction_id("test", str(test_id))
        return self.get_instruction_status(instruction_id, tenant_id)

    def delete_test_instruction(self, tenant_id: str, test_id: int) -> Optional[str]:
        """根据 test_id 删除测试指令。

        Args:
            test_id: Test identifier
            tenant_id: Tenant identifier

        Returns:
            Instruction ID if successful, None otherwise
        """
        instruction_id = self._generate_instruction_id("test", str(test_id))
        success = self.delete_instruction(tenant_id, instruction_id)
        if success:
            return instruction_id
        else:
            self.logger.error(f"Failed to delete test instruction {instruction_id} for tenant {tenant_id}")
            return None
        
    def create_subnet(self, tenant_id: str, vlan_id: int, subnet_gw_ip: str) -> Optional[str]:
        """创建一个子网。会同时创建 EIP,MON,NTP网通道

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:

            mon_m_ip = self.config.monnet.host
            mon_m_port = self.config.monnet.port

            # Allocate monnet IP
            monnet_ip = self.mon_ip_allocator.allocate_ip()
            if not monnet_ip:
                self.logger.error(f"Failed to allocate monnet IP for tenant {tenant_id}")
                return None

            res = self.create_subnet_gateway(tenant_id, vlan_id, subnet_gw_ip)
            self.create_eip(tenant_id, vlan_id)
            self.create_monnet(tenant_id, vlan_id, monnet_ip, mon_m_ip, mon_m_port)
            
            # ntpnet_ip is no longer used
            # self.logger.warning("NTP not implemented yet")
            
            return res
        except Exception as e:
            self.logger.error(f"Error creating subnet gateway for tenant {tenant_id}: {e}")
            # If IP was allocated but something else failed, release it.
            if 'monnet_ip' in locals() and monnet_ip:
                self.mon_ip_allocator.release_ip(monnet_ip)
            return None
        
    def delete_subnet(self, tenant_id: str, vlan_id: int) -> Optional[str]:
        """删除一个子网。会同时删除 EIP,MON,NTP网通道

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            monnet_ip: Monnet IP

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:
            self.delete_eip(tenant_id, vlan_id)
            self.delete_monnet_instruction(tenant_id, vlan_id)
            return self.delete_subnet_gateway_instruction(tenant_id, vlan_id)
        except Exception as e:
            self.logger.error(f"Error deleting subnet gateway for tenant {tenant_id}: {e}")
            return None
        
    def create_monnet(self, tenant_id: str, vlan_id: int, monnet_ip: str, mon_m_ip: str, mon_m_port: int) -> Optional[str]:
        """创建 MON 网络通道。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            monnet_ip: Monnet IP

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:
            # Generate unique instruction ID based on vlan_id
            instruction_id = self._generate_instruction_id("monnet", str(vlan_id))

            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            # Check if instruction already exists
            existing_instruction = self.shard_manager.get_instruction(shard_id, instruction_id)
            if existing_instruction:
                self.logger.info(f"Monnet instruction {instruction_id} already exists for tenant {tenant_id}")
                return instruction_id
            # Generate commands from template
            cmds = self._generate_monnet_commands(vlan_id, monnet_ip, mon_m_ip, mon_m_port)
            revocation = self._generate_monnet_revocation(vlan_id, monnet_ip, mon_m_ip, mon_m_port)

            # Create instruction
            instruction = {
                "id": instruction_id,
                "type": "monnet",
                "tenant_id": tenant_id,
                "vlan_id": vlan_id,
                "monnet_ip": monnet_ip,
                "shard_id": shard_id,
                "cmds": cmds,
                "revocation": revocation
            }

            # Put instruction to shard using instruction_id as key
            success = self.shard_manager.put_instruction(shard_id, instruction_id, instruction)
            if success:
                self.logger.info(f"Created monnet instruction {instruction_id} for tenant {tenant_id}")
                return instruction_id
            else:
                self.logger.error(f"Failed to create monnet instruction for tenant {tenant_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating monnet for tenant {tenant_id}: {e}")
            return None

    def delete_monnet_instruction(self, tenant_id: str, vlan_id: int) -> Optional[str]:
        """根据 vlan_id 和 monnet_ip 删除 MON 网络通道指令。

        Args:
            vlan_id: VLAN identifier
            monnet_ip: Monnet IP
            tenant_id: Tenant identifier

        Returns:
            Instruction ID if successful, None otherwise
        """
        instruction_id = self._generate_instruction_id("monnet", str(vlan_id))
        success = self.delete_instruction(tenant_id, instruction_id)
        if success:
            self.logger.info(f"Deleted monnet instruction {instruction_id} for tenant {tenant_id}")
            return instruction_id
        else:
            self.logger.error(f"Failed to delete monnet instruction {instruction_id} for tenant {tenant_id}")
            return None

    def create_subnet_gateway(self, tenant_id: str, vlan_id: int, subnet_gw_ip: str) -> Optional[str]:
        """创建子网网关。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:
            # Generate unique instruction ID based on vlan_id
            instruction_id = self._generate_instruction_id("subnet", str(vlan_id))

            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)

            # Check if instruction already exists
            existing_instruction = self.shard_manager.get_instruction(shard_id, instruction_id)
            if existing_instruction:
                self.logger.info(f"Subnet gateway instruction {instruction_id} already exists for tenant {tenant_id}")
                return instruction_id

            # Generate commands from template
            cmds = self._generate_subnet_gateway_commands(vlan_id, subnet_gw_ip)
            revocation = self._generate_subnet_gateway_revocation(vlan_id,subnet_gw_ip)

            # Create instruction
            instruction = {
                "id": instruction_id,
                "type": "subnet_gateway",
                "tenant_id": tenant_id,
                "vlan_id": vlan_id,
                "subnet_gw_ip": subnet_gw_ip,
                "shard_id": shard_id,
                "cmds": cmds,
                "revocation": revocation
            }

            # Put instruction to shard using instruction_id as key
            success = self.shard_manager.put_instruction(shard_id, instruction_id, instruction)

            if success:
                self.logger.info(f"Created subnet gateway instruction {instruction_id} for tenant {tenant_id}")
                return instruction_id
            else:
                self.logger.error(f"Failed to create subnet gateway instruction for tenant {tenant_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating subnet gateway for tenant {tenant_id}: {e}")
            return None

    def get_subnet_gateway_instruction_status(self, vlan_id: int, tenant_id: str) -> Optional[Dict[str, Any]]:
        """根据 vlan_id 获取子网网关指令状态。

        Args:
            vlan_id: VLAN identifier
            tenant_id: Tenant identifier

        Returns:
            Instruction data or None if not found
        """
        instruction_id = self._generate_instruction_id("subnet", str(vlan_id))
        return self.get_instruction_status(instruction_id, tenant_id)

    def delete_subnet_gateway_instruction(self, tenant_id: str, vlan_id: int) -> Optional[str]:
        """根据 vlan_id 删除子网网关指令。

        Args:
            vlan_id: VLAN identifier
            tenant_id: Tenant identifier

        Returns:
            True if successful
        """
        instruction_id = self._generate_instruction_id("subnet", str(vlan_id))
        success = self.delete_instruction(tenant_id, instruction_id)
        if success:
            self.logger.info(f"Deleted subnet gateway instruction {instruction_id} for tenant {tenant_id}")
            return instruction_id
        else:
            self.logger.error(f"Failed to delete subnet gateway instruction {instruction_id} for tenant {tenant_id}")
            return None

    def create_eip(self, tenant_id: str, vlan_id: int) -> Optional[str]:
        """创建 EIP。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:
            # Generate unique instruction ID based on vlan_id and eip
            instruction_id = self._generate_instruction_id("eip", str(vlan_id))

            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)

            # Check if instruction already exists
            existing_instruction = self.shard_manager.get_instruction(shard_id, instruction_id)
            if existing_instruction:
                self.logger.info(f"EIP instruction {instruction_id} already exists for tenant {tenant_id}")
                return instruction_id

            # Generate commands from template
            cmds = self._generate_eip_commands(vlan_id)

            revocation = self._generate_eip_revocation(vlan_id)

            # Create instruction
            instruction = {
                "id": instruction_id,
                "type": "eip",
                "tenant_id": tenant_id,
                "vlan_id": vlan_id,
                "shard_id": shard_id,
                "cmds": cmds,
                "revocation": revocation
            }

            # Put instruction to shard using instruction_id as key
            success = self.shard_manager.put_instruction(shard_id, instruction_id, instruction)

            if success:
                self.logger.info(f"Created EIP instruction {instruction_id} for tenant {tenant_id}")
                return instruction_id
            else:
                self.logger.error(f"Failed to create EIP instruction for tenant {tenant_id}")
                return None
            
        except Exception as e:
            self.logger.error(f"Error creating EIP for tenant {tenant_id}: {e}")
            return None
        
    # 删除 EIP
    def delete_eip(self, tenant_id: str, vlan_id: int) -> Optional[str]:
        """删除 EIP。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address

        Returns:
            Instruction ID if successful, None otherwise
        """
        instruction_id = self._generate_instruction_id("eip", str(vlan_id))
        success = self.delete_instruction(tenant_id, instruction_id)
        if success:
            self.logger.info(f"Deleted EIP instruction {instruction_id} for tenant {tenant_id}")
            return instruction_id
        else:
            self.logger.error(f"Failed to delete EIP instruction {instruction_id} for tenant {tenant_id}")
            return None
         
    # 挂载 EIP
    def create_eip_mount(self, tenant_id: str, vlan_id: int, eip: str, internal_ip: str) -> Optional[str]:
        """挂载 EIP，创建nft规则。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address

        Returns:
            Instruction ID if successful, None otherwise
        """
        try:
            eip_ga_way_ip = self.config.eip.gateway
            # Generate unique instruction ID based on vlan_id and eip for mount
            instruction_id = self._generate_instruction_id("eip_mount", str(vlan_id), eip)

            # Get shard for tenant
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)

            # Check if instruction already exists
            existing_instruction = self.shard_manager.get_instruction(shard_id, instruction_id)
            if existing_instruction:
                self.logger.info(f"EIP mount instruction {instruction_id} already exists for tenant {tenant_id}")
                return instruction_id

            # Generate commands from template (只创建nft规则和配置IP)
            cmds = self._generate_eip_mount_commands(vlan_id, eip, eip_ga_way_ip, internal_ip)
            revocation = self._generate_eip_mount_revocation(vlan_id, eip, eip_ga_way_ip, internal_ip)

            # Create instruction
            instruction = {
                "id": instruction_id,
                "type": "eip_mount",
                "tenant_id": tenant_id,
                "vlan_id": vlan_id,
                "eip": eip,
                "internal_ip": internal_ip,
                "shard_id": shard_id,
                "cmds": cmds,
                "revocation": revocation
            }

            # Put instruction to shard using instruction_id as key
            success = self.shard_manager.put_instruction(shard_id, instruction_id, instruction)

            if success:
                self.logger.info(f"Created EIP mount instruction {instruction_id} for tenant {tenant_id}")
                return instruction_id
            else:
                self.logger.error(f"Failed to create EIP mount instruction for tenant {tenant_id}")
                return None

        except Exception as e:
            self.logger.error(f"Error creating EIP mount for tenant {tenant_id}: {e}")
            return None
        
    # 卸载 EIP 
    def delete_eip_mount_instruction(self, tenant_id: str, vlan_id: int, eip: str) -> Optional[str]:
        """卸载 EIP 挂载。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address

        Returns:
            Instruction ID if successful, None otherwise
        """
        instruction_id = self._generate_instruction_id("eip_mount", str(vlan_id), eip)
        success = self.delete_instruction(tenant_id, instruction_id)
        if success:
            self.logger.info(f"Deleted EIP unmount instruction {instruction_id} for tenant {tenant_id}")
            return instruction_id
        else:
            self.logger.error(f"Failed to delete EIP unmount instruction {instruction_id} for tenant {tenant_id}")
            return None


    def get_eip_instruction_status(self, vlan_id: int, eip: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """根据 vlan_id 和 eip 获取 EIP 指令状态。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            tenant_id: Tenant identifier

        Returns:
            Instruction data or None if not found
        """
        instruction_id = self._generate_instruction_id("eip", str(vlan_id), eip)
        return self.get_instruction_status(instruction_id, tenant_id)

    def delete_eip_instruction(self, tenant_id: str, vlan_id: int, eip: str) -> Optional[str]:
        """根据 vlan_id 和 eip 删除 EIP 指令。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            tenant_id: Tenant identifier

        Returns:
            True if successful
        """
        instruction_id = self._generate_instruction_id("eip", str(vlan_id), eip)
        success = self.delete_instruction(tenant_id, instruction_id)
        if success:
            self.logger.info(f"Deleted EIP instruction {instruction_id} for tenant {tenant_id}")
            return instruction_id
        else:
            self.logger.error(f"Failed to delete EIP instruction {instruction_id} for tenant {tenant_id}")
            return None

    def _generate_test_commands(self, test_id: int) -> List[str]:
        """为创建测试生成命令。
        
        Args:
            test_id: Test identifier
            
        Returns:
            List of commands
        """
        return [
            f"echo 'Test command executed for {test_id} -1'",
            f"echo 'Test command executed for {test_id} -2'",
            f"echo 'Test command executed for {test_id} -3'"
        ]
    
    def _generate_test_revocation(self, test_id: int) -> List[str]:
        """为测试生成撤销命令。
        
        Args:
            test_id: Test identifier
            
        Returns:
            List of revocation commands
        """
        return [
            f"echo 'Test revocation executed for {test_id} -1'",
            f"echo 'Test revocation executed for {test_id} -2'",
            f"echo 'Test revocation executed for {test_id} -3'",
            #f"echo 'Test revocation executed for {test_id} -4'"
        ]
    
    
    def _generate_subnet_gateway_commands(self, vlan_id: int, subnet_gw_ip: str) -> List[str]:
        """为创建子网网关生成命令。同时创建命名空间和nft表链。

        Args:
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP

        Returns:
            List of commands
        """
        return [
            # 创建命名空间
            f"ip netns add ns-vlan{vlan_id}",
            # 创建veth对
            f"ip link add v-lan-host-{vlan_id} type veth peer name v-lan-ns-{vlan_id}",
            f"ip link set v-lan-ns-{vlan_id} netns ns-vlan{vlan_id}",
            # 配置IP地址
            f"ip netns exec ns-vlan{vlan_id} ip addr add {subnet_gw_ip}/24 dev v-lan-ns-{vlan_id}",
            f"ip netns exec ns-vlan{vlan_id} ip addr",
            # 添加到OVS桥
            f"ovs-vsctl add-port br-vlan v-lan-host-{vlan_id} tag={vlan_id}",
            # 启动网卡
            f"ip link set v-lan-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-lan-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up",
            f"ip link set v-lan-host-{vlan_id} up",
            # 创建nft表和链
            f"ip netns exec ns-vlan{vlan_id} nft add table ip nat",
            f"ip netns exec ns-vlan{vlan_id} nft add chain ip nat PREROUTING {{ type nat hook prerouting priority -100 \\; }}",
            f"ip netns exec ns-vlan{vlan_id} nft add chain ip nat POSTROUTING {{ type nat hook postrouting priority 100 \\; }}",
            f"ip netns exec ns-vlan{vlan_id} nft list table ip nat"
        ]
    
    def _generate_subnet_gateway_revocation(self, vlan_id: int, subnet_gw_ip: str) -> List[str]:
        """为子网网关生成撤销命令。同时删除nft表链和命名空间。

        Args:
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP (保持参数一致性)

        Returns:
            List of revocation commands
        """
        return [
            # 删除nft表（会自动删除所有链和规则）
            f"ip netns exec ns-vlan{vlan_id} nft delete table ip nat || true",
            # 删除OVS端口
            f"ovs-vsctl --if-exists del-port br-vlan v-lan-host-{vlan_id}",
            # 删除veth对
            f"ip link del v-lan-host-{vlan_id}",
            # 删除命名空间
            f"ip netns del ns-vlan{vlan_id}"
        ]
    
    # 监控网配置命令
    def _generate_monnet_commands(self, vlan_id: int, monnet_ip: str, mon_m_ip: str, mon_m_port: int) -> List[str]:
        """为创建 MON 网络通道生成命令。同时创建nft规则。
        Args:
            vlan_id: VLAN identifier
            monnet_ip: Monnet IP
        Example:
          #创建对应的监控 虚拟网卡对
          ip link add v-mon-host-${VLAN_ID} type veth peer name v-mon-ns-${VLAN_ID}
          ip link set v-mon-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
          ip netns exec ns-vlan${VLAN_ID} ip addr add ${MON_IP}/24 dev v-mon-ns-${VLAN_ID}
          #把mon ip配置到监控网桥
          ovs-vsctl add-port br-mon v-mon-host-${VLAN_ID}
          #启动网卡
          ip link set v-mon-host-${VLAN_ID} up
          ip netns exec ns-vlan${VLAN_ID} ip link set v-mon-ns-${VLAN_ID} up
          ip netns exec ns-vlan${VLAN_ID} ip link set lo up
        Returns:
            List of commands
        """
        return [
            # 创建veth对
            f"ip link add v-mon-host-{vlan_id} type veth peer name v-mon-ns-{vlan_id}",
            f"ip link set v-mon-ns-{vlan_id} netns ns-vlan{vlan_id}",
            # 配置IP地址
            f"ip netns exec ns-vlan{vlan_id} ip addr add {monnet_ip}/24 dev v-mon-ns-{vlan_id}",
            # 添加到监控网桥
            f"ovs-vsctl add-port br-mon v-mon-host-{vlan_id}",
            # 启动网卡
            f"ip link set v-mon-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-mon-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up",
            # 添加监控网络的nft规则
            f"ip netns exec ns-vlan{vlan_id} nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to {mon_m_ip}:{mon_m_port}",
            f"ip netns exec ns-vlan{vlan_id} nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 30428 counter snat to ************"
        ]
    
    def _generate_monnet_revocation(self, vlan_id: int, monnet_ip: str, mon_m_ip: str, mon_m_port: int) -> List[str]:
        """为 MON 网络通道生成撤销命令。只删除nft规则，不删除表和链。

        Args:
            vlan_id: VLAN identifier
            monnet_ip: Monnet IP

        Returns:
            List of revocation commands
        """
        return [
            # 删除OVS端口
            f"ovs-vsctl --if-exists del-port br-mon v-mon-host-{vlan_id}",
            # 删除veth对
            f"ip link del v-mon-host-{vlan_id}"
        ]
    

    # 创建 EIP 虚拟网卡命令
    def _generate_eip_commands(self, vlan_id: int) -> List[str]:
        """为创建 EIP 生成命令。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address
        Example:
            #创建对应的EIP 虚拟网卡对
            ip link add v-eip-host-${VLAN_ID} type veth peer name v-eip-ns-${VLAN_ID}
            ip link set v-eip-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
            #把EIP配置到eip 网桥
            ovs-vsctl add-port br-eip v-eip-host-${VLAN_ID}
            #启动网卡
            ip link set v-eip-host-${VLAN_ID} up
            ip netns exec ns-vlan${VLAN_ID} ip link set v-eip-ns-${VLAN_ID} up
            ip netns exec ns-vlan${VLAN_ID} ip link set lo up
            #添加默认网关
            ip netns exec ns-vlan${VLAN_ID} ip route add default via ${EIP_GA_WAY_IP} dev v-eip-ns-${VLAN_ID}
            ip netns exec ns-vlan${VLAN_ID} ip addr
        Returns:
            List of commands
        """
        return [
            # 创建对应的EIP 虚拟网卡对
            f"ip link add v-eip-host-{vlan_id} type veth peer name v-eip-ns-{vlan_id}",
            f"ip link set v-eip-ns-{vlan_id} netns ns-vlan{vlan_id}",
            #把EIP配置到eip 网桥
            f"ovs-vsctl add-port br-eip v-eip-host-{vlan_id}",
            #启动网卡
            f"ip link set v-eip-host-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set v-eip-ns-{vlan_id} up",
            f"ip netns exec ns-vlan{vlan_id} ip link set lo up",
            #添加默认网关
            f"ip netns exec ns-vlan{vlan_id} ip addr"
        ]

    def _generate_eip_revocation(self, vlan_id: int) -> List[str]:
        """为 EIP 生成撤销命令。

        Args:
            vlan_id: VLAN identifier
            gateway_ip: Gateway IP address

        Returns:
            List of revocation commands
        """
        return [
            f"ovs-vsctl --if-exists del-port br-eip v-eip-host-{vlan_id}",
            f"ip link del v-eip-host-{vlan_id}"
        ]
    
    # EIP 挂载配置命令
    def _generate_eip_mount_commands(self, vlan_id: int, eip: str, eip_ga_way_ip: str, internal_ip: str) -> List[str]:
        """为创建 EIP 挂载生成命令。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address
        Example:
            #创建EIP
            ip netns exec ns-vlan${VLAN_ID} ip addr add ${EIP}/24 dev v-eip-ns-${VLAN_ID}
            # 添加 nat , 把子网和 EIP进行对应
            ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr ${EIP} counter dnat to ${ZW_IP}
            #删除后再通过 nft 增加
            ip netns exec ns-vlan${VLAN_ID} nft add table ip nat
            ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING \
                ip saddr ${ZW_IP} \
                ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } \
                snat to ${EIP}
                
            ip netns exec ns-vlan${VLAN_ID}  nft list table ip nat
        Returns:
            List of commands
        """
        return [
            # 配置EIP地址
            f"ip netns exec ns-vlan{vlan_id} ip addr add {eip}/24 dev v-eip-ns-{vlan_id}",
            # 添加默认路由
            f"ip netns exec ns-vlan{vlan_id} ip route add default via {eip_ga_way_ip} dev v-eip-ns-{vlan_id} || true",
            # 添加nft规则（表和链已在子网网关创建时建立）
            f"ip netns exec ns-vlan{vlan_id} nft add rule ip nat PREROUTING ip daddr {eip} counter dnat to {internal_ip}",
            f"ip netns exec ns-vlan{vlan_id} nft add rule ip nat POSTROUTING ip saddr {internal_ip} ip daddr != {{ 10.0.0.0/8, **********/12, ***********/24,***********/24 }} snat to {eip}",
            # 显示当前规则
            f"ip netns exec ns-vlan{vlan_id} nft list table ip nat"
        ]
    
    def _generate_eip_mount_revocation(self, vlan_id: int, eip: str, eip_ga_way_ip: str, internal_ip: str) -> List[str]:
        """为 EIP 挂载生成撤销命令。只删除nft规则，不删除表和链。

        Args:
            vlan_id: VLAN identifier
            eip: External IP address
            gateway_ip: Gateway IP address
            internal_ip: Internal IP address

        Returns:
            List of revocation commands
        """
        return [
            # 删除EIP地址
            f"ip netns exec ns-vlan{vlan_id} ip addr del {eip}/24 dev v-eip-ns-{vlan_id} || true",
            # 删除对应的nft规则（通过handle删除更精确，这里使用规则匹配删除）
            f"ip netns exec ns-vlan{vlan_id} nft delete rule ip nat PREROUTING handle $(ip netns exec ns-vlan{vlan_id} nft -a list chain ip nat PREROUTING | grep 'dnat to {internal_ip}' | awk '{{print $NF}}')",
            f"ip netns exec ns-vlan{vlan_id} nft delete rule ip nat POSTROUTING handle $(ip netns exec ns-vlan{vlan_id} nft -a list chain ip nat POSTROUTING | grep 'snat to {eip}' | awk '{{print $NF}}')",
            # 删除默认路由
            # f"ip netns exec ns-vlan{vlan_id} ip route del default via {eip_ga_way_ip} dev v-eip-ns-{vlan_id} || true",
            # 显示剩余规则
            f"ip netns exec ns-vlan{vlan_id} nft list table ip nat"
        ]

    def get_instruction_status(self, instruction_id: str, tenant_id: str) -> Optional[Dict[str, Any]]:
        """获取指令的状态。

        Args:
            instruction_id: Instruction identifier
            tenant_id: Tenant identifier

        Returns:
            Instruction data or None if not found
        """
        try:
            shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            return self.shard_manager.get_instruction(shard_id, instruction_id)

        except Exception as e:
            self.logger.error(f"Error getting instruction status {instruction_id}: {e}")
            return None

    def delete_instruction(self, tenant_id: str, instruction_id: str) -> bool:
        """删除指令。

        Args:
            instruction_id: Instruction identifier
            tenant_id: Tenant identifier

        Returns:
            True if successful
        """
        try:
          shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
          return self.shard_manager.delete_instruction(shard_id, instruction_id)

        except Exception as e:
            self.logger.error(f"Error deleting instruction {instruction_id}: {e}")
            return False
