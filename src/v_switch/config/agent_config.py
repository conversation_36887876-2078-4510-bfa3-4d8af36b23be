"""
Agent configuration for v-switch agent.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from .base_config import BaseConfig


@dataclass
class AgentSettings:
    """Agent-specific settings."""
    agent_id: str = ""
    check_env: bool = True
    heartbeat_interval: float = 30.0
    max_retries: int = 3


class AgentConfig(BaseConfig):
    """Configuration for v-switch agent."""

    def __init__(self, config_file: Optional[str] = None):
        """Initialize agent configuration.

        Args:
            config_file: Path to YAML configuration file
        """
        # Initialize agent settings first
        self.agent = AgentSettings()

        # Call parent init (which will call _apply_env_overrides)
        super().__init__(config_file)
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """Update configuration from dictionary.
        
        Args:
            config_data: Configuration dictionary
        """
        # Call parent method for common configs
        super()._update_from_dict(config_data)
        
        # Update agent-specific config
        if 'agent' in config_data:
            agent_config = config_data['agent']
            for key, value in agent_config.items():
                if hasattr(self.agent, key):
                    setattr(self.agent, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        config_dict = super().to_dict()
        config_dict['agent'] = {
            'agent_id': self.agent.agent_id,
            'check_env': self.agent.check_env,
            'heartbeat_interval': self.agent.heartbeat_interval,
            'max_retries': self.agent.max_retries,
        }
        return config_dict
    
    def get_register_key(self) -> str:
        """Get ETCD key for agent registration.
        
        Returns:
            ETCD key path for agent registration
        """
        return f"/network/agent/register/{self.agent.agent_id}"
    
    def get_task_prefix(self) -> str:
        """Get ETCD prefix for agent tasks.
        
        Returns:
            ETCD prefix path for agent tasks
        """
        return f"/network/agent/task/{self.agent.agent_id}"
    
    def get_lease_ttl(self) -> int:
        """Get lease TTL for heartbeat (3x heartbeat interval).

        Returns:
            Lease TTL in seconds
        """
        return int(self.agent.heartbeat_interval * 3)

    def _get_env_mappings(self) -> Dict[str, tuple]:
        """Get mapping of environment variables to configuration paths.

        Returns:
            Dictionary mapping env var names to (section, key, type_converter) tuples
        """
        # Get base mappings from parent class
        mappings = super()._get_env_mappings()

        # Add agent-specific mappings
        agent_mappings = {
            'VSWITCH_AGENT_ID': ('agent', 'agent_id', str),
            'VSWITCH_AGENT_CHECK_ENV': ('agent', 'check_env', self._str_to_bool),
            'VSWITCH_AGENT_HEARTBEAT_INTERVAL': ('agent', 'heartbeat_interval', float),
            'VSWITCH_AGENT_MAX_RETRIES': ('agent', 'max_retries', int),
        }

        mappings.update(agent_mappings)
        return mappings
