"""
Server configuration for v-switch core service.
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from .base_config import BaseConfig


@dataclass
class ServerSettings:
    """服务器特定的设置。"""
    port: int = 30090
    shard_count: int = 32

class SubNetSettings:
    """子网特定的设置。"""
    vlan_min: int = 1500
    vlan_max: int = 2500
@dataclass
class EipSettings:
    """服务器特定的设置。"""
    gateway: str = ""

@dataclass
class MonnetSettings:
    """服务器特定的设置。"""
    ip_min: str = ""
    ip_max: str = ""
    host: str = ""
    port: int = 0

class ServerConfig(BaseConfig):
    """v-switch 核心服务的配置。"""

    def __init__(self, config_file: Optional[str] = None):
        """初始化服务配置。

        Args:
            config_file: Path to YAML configuration file
        """
        # Initialize server settings first
        self.server = ServerSettings()
        self.eip = EipSettings()
        self.monnet = MonnetSettings()
        self.subnet = SubNetSettings()

        # Call parent init (which will call _apply_env_overrides)
        super().__init__(config_file)
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """从字典中更新配置。
        
        Args:
            config_data: Configuration dictionary
        """
        # 调用常见配置的父方法
        super()._update_from_dict(config_data)
        
        # 更新服务器特定的配置
        if 'server' in config_data:
            server_config = config_data['server']
            for key, value in server_config.items():
                if hasattr(self.server, key):
                    setattr(self.server, key, value)
        if 'subnet' in config_data:
            subnet_config = config_data['subnet']
            for key, value in subnet_config.items():
                if hasattr(self.subnet, key):
                    setattr(self.subnet, key, value)
        if 'eip' in config_data:
            eip_config = config_data['eip']
            for key, value in eip_config.items():
                if hasattr(self.eip, key):
                    setattr(self.eip, key, value)
        if 'monnet' in config_data:
            monnet_config = config_data['monnet']
            for key, value in monnet_config.items():
                if hasattr(self.monnet, key):
                    setattr(self.monnet, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典。
        
        Returns:
            Configuration as dictionary
        """
        config_dict = super().to_dict()
        config_dict['server'] = {
            'port': self.server.port,
            'shard_count': self.server.shard_count,
        }
        config_dict['eip'] = {
            'gateway': self.eip.gateway,
        }
        config_dict['monnet'] = {
            'host': self.monnet.host,
            'port': self.monnet.port,
        }
        return config_dict
    
    def get_shard_count(self) -> int:
        """获取分片数量。
        
        Returns:
            Number of shards
        """
        return self.server.shard_count
    
    def get_shard_paths(self) -> list[str]:
        """获取ETCD的分片路径列表。

        Returns:
            List of shard paths like ['/network/server/instruct/1', ...]
        """
        return [f"/network/server/instruct/{i}"
            for i in range(self.server.shard_count)]

    def _get_env_mappings(self) -> Dict[str, tuple]:
        """Get mapping of environment variables to configuration paths.

        Returns:
            Dictionary mapping env var names to (section, key, type_converter) tuples
        """
        # Get base mappings from parent class
        mappings = super()._get_env_mappings()

        # Add server-specific mappings
        server_mappings = {
            'VSWITCH_SERVER_PORT': ('server', 'port', int),
            'VSWITCH_SERVER_SHARD_COUNT': ('server', 'shard_count', int),
        }

        mappings.update(server_mappings)
        return mappings
