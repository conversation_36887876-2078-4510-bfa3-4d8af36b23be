"""
Main agent class for v-switch.
"""

import json
import logging
import threading
import time
from typing import Dict, Any, List, Optional, Set
from etcd3.events import PutEvent, DeleteEvent
from ..common.etcd_client import ETCDClient
from ..config.agent_config import AgentConfig
from .heartbeat_manager import HeartbeatManager
from .command_executor import CommandExecutor
from .environment_checker import EnvironmentChecker


class VSwitchAgent:
    """Main v-switch agent class."""
    
    METADATA_KEY = "/network/server/metadata"
    INSTRUCT_PREFIX = "/network/server/instruct/"
    
    def __init__(self, config: AgentConfig, dry_run: bool = False):
        """初始化 agent.
        
        Args:
            config: Agent configuration
            dry_run: 如果为 True，则命令会被记录但不会执行
        """
        self.config = config
        self.dry_run = dry_run
        self.logger = logging.getLogger(__name__)
        
        # Initialize ETCD client
        self.etcd_client = ETCDClient(
            host=config.etcd.host,
            port=config.etcd.port,
            timeout=config.etcd.timeout,
            username=config.etcd.username,
            password=config.etcd.password
        )
        
        # Initialize components
        self.heartbeat_manager = HeartbeatManager(self.etcd_client, config)
        self.command_executor = CommandExecutor(dry_run=dry_run)
        self.env_checker = EnvironmentChecker()
        
        # State management
        self._running = False
        self._assigned_shards: Set[str] = set()
        self._metadata_watch_thread = None
        self._instruction_watch_threads: Dict[str, threading.Thread] = {}
    
    def start(self) -> bool:
        """启动 agent
        
        Returns:
            True if successful
        """
        try:
            self.logger.info("Starting v-switch agent...")
            
            # 环境检查，检查不通过时抛出异常
            if self.config.agent.check_env:
                env_ok, env_errors = self.env_checker.check_all()
                if not env_ok:
                    self.heartbeat_manager.set_status("error")
                    raise RuntimeError(f"Environment check failed: {env_errors}")
            self.heartbeat_manager.set_status("running")

            # 连接到 ETCD
            self.etcd_client.connect()
            
            # 开始监控服务器元数据
            self._start_metadata_monitoring()

            # 启动心跳管理器
            if not self.heartbeat_manager.start():
                self.logger.error("Failed to start heartbeat manager")
                return False
            
            self._running = True
        
            self.logger.info("V-switch agent started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start agent: {e}")
            return False
    
    def stop(self) -> None:
        """停止 agent."""
        try:
            self.logger.info("Stopping v-switch agent...")
            self._running = False
            
            # 停止指令监控
            self._stop_instruction_watching()
            
            # 停止服务器元数据监控
            if self._metadata_watch_thread and self._metadata_watch_thread.is_alive():
                self._metadata_watch_thread.join(timeout=5.0)
            
            # 停止心跳管理器
            self.heartbeat_manager.stop()
            
            self.logger.info("V-switch agent stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping agent: {e}")

    def _start_metadata_monitoring(self) -> None:
        """开始监听服务器元数据以获取分片分配变更."""

        def monitor_metadata():
            """监控服务器元数据变化."""
            try:
                self.logger.info("Starting metadata monitoring")
                # 监控元数据变化
                def handle_metadata_event(event):
                    if isinstance(event, PutEvent):
                        self._update_shard_assignments(event)
                
                self.etcd_client.watch_key(self.METADATA_KEY, handle_metadata_event)
                
            except Exception as e:
                self.logger.error(f"Error in metadata monitoring: {e}")
        # 启动元数据监控线程
        self._metadata_watch_thread = threading.Thread(
            target=monitor_metadata,
            name="MetadataMonitor",
            daemon=True
        )
        self._metadata_watch_thread.start()
    
    def _update_shard_assignments(self, event: PutEvent) -> None:
        """根据服务器元数据更新分片分配。"""
        try:
            if not event:
                logger.warning("No metadata event")
                return
            # 获取当前元数据
            # metadata, _ = self.etcd_client.get_json(self.METADATA_KEY)
            # if not metadata:
            #     self.logger.warning("No server metadata found")
            #     return
            
            if event:
                metadata = json.loads(event.value.decode('utf-8'))
            
            # 在元数据中查找当前 agent 的信息
            agent_data = None
            for agent in metadata.get("agent", []):
                if agent.get("agent_id") == self.config.agent.agent_id:
                    agent_data = agent
                    break
            
            if not agent_data:
                self.logger.debug(f"Agent {self.config.agent.agent_id} not found in metadata")
                return
            
            # 获取已分配的分片
            new_shards = set(agent_data.get("shard", []))
            
            # 检查是否有变化
            if new_shards != self._assigned_shards:
                self.logger.info(f"Shard assignment changed: {self._assigned_shards} -> {new_shards}")
                
                # 处理移除的分片
                removed_shards = self._assigned_shards - new_shards
                for shard in removed_shards:
                    self._handle_shard_removal(shard)
                
                # 处理新增的分片
                added_shards = new_shards - self._assigned_shards
                for shard in added_shards:
                    self._handle_shard_addition(shard)
                
                # 更新已分配的分片
                self._assigned_shards = new_shards
            
        except Exception as e:
            self.logger.error(f"Error updating shard assignments: {e}")
    
    def _handle_shard_addition(self, shard: str) -> None:
        """处理新增的分片。
        
        Args:
            shard: Shard identifier
        """
        try:
            self.logger.info(f"Adding shard: {shard}")
            # 处理新增分片的指令
            self._handle_added_shard_instructions(shard)
            # Start watching instructions for this shard
            self._start_shard_watching(shard)
            
        except Exception as e:
            self.logger.error(f"Error handling shard addition {shard}: {e}")
    
    def _handle_shard_removal(self, shard: str) -> None:
        """处理移除的分片。
        
        Args:
            shard: Shard identifier
        """
        try:
            self.logger.info(f"Removing shard: {shard}")
            
            # 停止监控指令
            self._stop_shard_watching(shard)
            
            # 执行撤销任务的命令
            self._revoke_shard_tasks(shard)
            
        except Exception as e:
            self.logger.error(f"Error handling shard removal {shard}: {e}")
    
    def _handle_added_shard_instructions(self, shard: str) -> None:
        """全量处理新增分片的指令。
        节点切换时，新增分片内的指令在当前节点可能尚未执行，因此需要全量处理一次

        Args:
            shard: Shard identifier
        """
        try:
            # 获取分片中的所有指令
            shard_prefix = f"{self.INSTRUCT_PREFIX}{shard}/"
            instructions = self.etcd_client.get_prefix(shard_prefix)
            
            for key, value in instructions:
                try:
                    self._handle_instruction_creation(shard, value)
                except Exception as e:
                    instruction_id = key.replace(shard_prefix, "")
                    self.logger.error(f"Error handling instruction {instruction_id}: {e}")

        except Exception as e:
            self.logger.error(f"Error handling added shard instructions for {shard}: {e}")

    def _start_shard_watching(self, shard: str) -> None:
        """开始监控分片的指令。
        
        Args:
            shard: Shard identifier
        """
        def watch_shard_instructions():
            """监控分片的指令变化。"""
            try:
                shard_prefix = f"{self.INSTRUCT_PREFIX}{shard}/"
                # self.logger.info(f"Starting instruction watching for shard: {shard}")
                # 处理指令事件
                def handle_instruction_event(event):
                    try:
                        key = event.key.decode('utf-8')
                        instruction_id = key.replace(shard_prefix, "")
                        
                        if isinstance(event, DeleteEvent):
                            # 执行撤销命令
                            self._handle_instruction_deletion(shard, instruction_id)
                        elif isinstance(event, PutEvent):
                            # 执行新增指令
                            self._handle_instruction_creation(shard, event.value.decode('utf-8'))
                            
                    except Exception as e:
                        self.logger.error(f"Error handling instruction event: {e}")
                
                self.etcd_client.watch_prefix(shard_prefix, handle_instruction_event)
                
            except Exception as e:
                self.logger.error(f"Error watching shard {shard}: {e}")
        
        # 开始监视此分片的线程
        thread = threading.Thread(
            target=watch_shard_instructions,
            name=f"ShardWatch-{shard}",
            daemon=True
        )
        thread.start()
        self._instruction_watch_threads[shard] = thread

    def _stop_shard_watching(self, shard: str) -> None:
        """停止监听分片的指令。

        Args:
            shard: Shard identifier
        """
        if shard in self._instruction_watch_threads:
            _ = self._instruction_watch_threads.pop(shard)
            # Note: etcd3 watch threads don't have a clean way to stop
            # They will stop when the agent stops
            self.logger.info(f"Stopped instruction watching for shard: {shard}")

    def _stop_instruction_watching(self) -> None:
        """停止所有指令监听线程。"""
        for shard in list(self._instruction_watch_threads.keys()):
            self._stop_shard_watching(shard)

    def _revoke_shard_tasks(self, shard: str) -> None:
        """撤销分片的所有任务。

        Args:
            shard: Shard identifier
        """
        try:
            # Get all tasks for this agent and shard
            task_prefix = f"{self.config.get_task_prefix()}/{shard}/"

            for key, value in self.etcd_client.get_prefix(task_prefix):
                try:
                    task_data = json.loads(value)
                    instruction = task_data.get("instruct", {})
                    revocation_commands = instruction.get("revocation", [])

                    if revocation_commands:
                        self.logger.info(f"Revoking task: {key}")
                        self._execute_revocation_commands(revocation_commands, key)

                    # Delete task
                    self.etcd_client.delete(key)

                except Exception as e:
                    self.logger.error(f"Error revoking task {key}: {e}")

        except Exception as e:
            self.logger.error(f"Error revoking shard tasks for {shard}: {e}")

    def _handle_instruction_deletion(self, shard: str, instruction_id: str) -> None:
        """处理指令删除事件。

        Args:
            shard: Shard identifier
            instruction_id: Instruction identifier
            event: ETCD event
        """
        try:
            # 检查是否已存在任务, 如果不存在则跳过执行
            task_key = f"{self.config.get_task_prefix()}/{shard}/{instruction_id}"
            task_value, _ = self.etcd_client.get(task_key)
            if not task_value:
                self.logger.info(f"Task does not exist for instruction: {instruction_id}, skip execution")
                return
            # 从任务中获取撤销命令
            task_data = json.loads(task_value)
            instruction_data = task_data.get("instruct", {})
            revocation_commands = instruction_data.get("revocation", [])
            # 执行撤销命令
            if revocation_commands:
                task_key = f"{self.config.get_task_prefix()}/{shard}/{instruction_id}"
                self.logger.info(f"Executing revocation for deleted instruction: {instruction_id}")
                self._execute_revocation_commands(revocation_commands, task_key)
            # 删除任务
            self.etcd_client.delete(task_key)

        except Exception as e:
            self.logger.error(f"Error handling instruction deletion {instruction_id}: {e}")

    def _handle_instruction_creation(self, shard: str, instruction: str) -> None:
        """处理指令创建事件。

        Args:
            shard: Shard identifier
            instruction_id: Instruction identifier
            event: ETCD event
        """
        try:
            # 解析指令内容
            instruction_data = json.loads(instruction)
            instruction_id = instruction_data.get("id")
            commands = instruction_data.get("cmds", [])
            # 检查是否已存在任务, 如果已存在则跳过执行
            task_key = f"{self.config.get_task_prefix()}/{shard}/{instruction_id}"
            task_value, _ = self.etcd_client.get(task_key)
            self.logger.info(f"task_exists: {task_value}")
            if task_value:
                self.logger.info(f"Task already exists for instruction: {instruction_id}, skip execution")
                return

            if commands:
                self.logger.info(f"Executing commands for instruction: {instruction_id}")
                self._execute_instruction_commands(commands, instruction_data, task_key)

        except Exception as e:
            self.logger.error(f"Error handling instruction creation {instruction_id}: {e}")

    def _execute_instruction_commands(self, commands: List[str], instruction_data: Dict[str, Any], task_key: str) -> None:
        """Execute instruction commands.

        Args:
            commands: List of commands to execute
            instruction_data: Full instruction data
            task_key: Task key for status tracking
        """
        try:
            # Create task entry with "exec" status
            task_data = {
                "instruct": instruction_data,
                "status": "exec",
                "message": "Executing commands"
            }
            self.etcd_client.put_json(task_key, task_data)

            # 执行指令
            success, results = self.command_executor.execute_commands(commands)

            # 更新任务状态
            if success:
                task_data["status"] = "ok"
                task_data["message"] = "Commands executed successfully"
                self.logger.info(f"Successfully executed instruction: {instruction_data.get('id', 'unknown')}")
            else:
                task_data["status"] = "err"
                failed_commands = [r["command"] for r in results if not r["success"]]
                task_data["message"] = f"Failed commands: {failed_commands}"
                self.logger.error(f"Failed to execute instruction: {instruction_data.get('id', 'unknown')}")

            # Update task in ETCD
            self.etcd_client.put_json(task_key, task_data)

        except Exception as e:
            # Update task with error status
            task_data = {
                "instruct": instruction_data,
                "status": "err",
                "message": f"Execution error: {e}"
            }
            self.etcd_client.put_json(task_key, task_data)
            self.logger.error(f"Error executing instruction commands: {e}")

            # Set agent status to error
            self.heartbeat_manager.set_status("error")

    def _execute_revocation_commands(self, commands: List[str], task_key: str) -> None:
        """Execute revocation commands.

        Args:
            commands: List of revocation commands
            task_key: Task key for logging
        """
        try:
            self.logger.info(f"Executing revocation commands for task: {task_key}")
            success, results = self.command_executor.execute_commands(commands, stop_on_error=False)

            if not success:
                failed_commands = [r["command"] for r in results if not r["success"]]
                self.logger.warning(f"Some revocation commands failed: {failed_commands}")
            else:
                self.logger.info(f"Successfully executed revocation commands for task: {task_key}")

        except Exception as e:
            self.logger.error(f"Error executing revocation commands: {e}")

    def is_running(self) -> bool:
        """Check if agent is running.

        Returns:
            True if running
        """
        return self._running

    def get_assigned_shards(self) -> Set[str]:
        """Get currently assigned shards.

        Returns:
            Set of assigned shard identifiers
        """
        return self._assigned_shards.copy()

    def get_status(self) -> Dict[str, Any]:
        """Get agent status information.

        Returns:
            Status information dictionary
        """
        return {
            "agent_id": self.config.agent.agent_id,
            "running": self._running,
            "heartbeat_status": self.heartbeat_manager.get_status(),
            "assigned_shards": list(self._assigned_shards),
            "dry_run": self.dry_run
        }
