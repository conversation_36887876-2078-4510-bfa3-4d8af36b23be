"""
Command executor for v-switch agent.
"""

import subprocess
import logging
import time
from typing import List, Tuple, Dict, Any, Optional


class CommandExecutor:
    """执行用于网络配置的系统命令。"""
    
    def __init__(self, dry_run: bool = False):
        """初始化命令执行器。
        
        Args:
            dry_run: If True, commands are logged but not executed
        """
        self.dry_run = dry_run
        self.logger = logging.getLogger(__name__)
    
    def execute_command(self, command: str, timeout: int = 30) -> Tuple[bool, str, str]:
        """执行单个命令。
        
        Args:
            command: Command to execute
            timeout: Command timeout in seconds
            
        Returns:
            Tuple of (success, stdout, stderr)
        """
        if self.dry_run:
            self.logger.info(f"DRY RUN: {command}")
            return True, f"DRY RUN: {command}", ""
        
        try:
            self.logger.debug(f"Executing: {command}")
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            success = result.returncode == 0
            stdout = result.stdout.strip()
            stderr = result.stderr.strip()
            
            if success:
                self.logger.debug(f"Command succeeded: {command}")
                if stdout:
                    self.logger.debug(f"STDOUT: {stdout}")
            else:
                self.logger.error(f"Command failed: {command}")
                self.logger.error(f"Return code: {result.returncode}")
                if stderr:
                    self.logger.error(f"STDERR: {stderr}")
            
            return success, stdout, stderr
            
        except subprocess.TimeoutExpired:
            error_msg = f"Command timed out after {timeout}s: {command}"
            self.logger.error(error_msg)
            return False, "", error_msg
            
        except Exception as e:
            error_msg = f"Command execution failed: {e}"
            self.logger.error(error_msg)
            return False, "", error_msg
    
    def execute_commands(self, commands: List[str], stop_on_error: bool = True) -> Tuple[bool, List[Dict[str, Any]]]:
        """执行一组命令。
        
        Args:
            commands: List of commands to execute
            stop_on_error: If True, stop execution on first error
            
        Returns:
            Tuple of (overall_success, list_of_results)
        """
        results = []
        overall_success = True
        
        for i, command in enumerate(commands):
            self.logger.info(f"Executing command {i+1}/{len(commands)}: {command}")
            
            success, stdout, stderr = self.execute_command(command)
            
            result = {
                "command": command,
                "success": success,
                "stdout": stdout,
                "stderr": stderr,
                "index": i
            }
            results.append(result)
            
            if not success:
                overall_success = False
                if stop_on_error:
                    self.logger.error(f"Stopping execution due to error in command {i+1}")
                    break
        
        return overall_success, results
    
    def execute_with_retry(self, command: str, max_retries: int = 3, 
                          retry_delay: float = 1.0) -> Tuple[bool, str, str]:
        """执行命令，失败时重试。
        
        Args:
            command: Command to execute
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
            
        Returns:
            Tuple of (success, stdout, stderr)
        """
        last_error = ""
        
        for attempt in range(max_retries + 1):
            if attempt > 0:
                self.logger.info(f"Retrying command (attempt {attempt+1}/{max_retries+1}): {command}")
                time.sleep(retry_delay)
            
            success, stdout, stderr = self.execute_command(command)
            
            if success:
                if attempt > 0:
                    self.logger.info(f"Command succeeded on retry {attempt}")
                return success, stdout, stderr
            
            last_error = stderr or "Unknown error"
        
        self.logger.error(f"Command failed after {max_retries + 1} attempts: {command}")
        return False, "", last_error
    
    def check_command_exists(self, command: str) -> bool:
        """检查系统中是否存在命令。
        
        Args:
            command: Command name to check
            
        Returns:
            True if command exists
        """
        try:
            result = subprocess.run(
                ['which', command],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
            
        except Exception:
            return False
    
    def validate_commands(self, commands: List[str]) -> List[str]:
        """验证所有必需的命令都存在。
        
        Args:
            commands: List of commands to validate
            
        Returns:
            List of missing commands
        """
        missing = []
        
        for command in commands:
            # Extract the actual command name (first word)
            cmd_name = command.strip().split()[0] if command.strip() else ""
            
            if cmd_name and not self.check_command_exists(cmd_name):
                missing.append(cmd_name)
        
        return missing
    
    def get_execution_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取命令执行结果的摘要。
        
        Args:
            results: List of command execution results
            
        Returns:
            Execution summary
        """
        total = len(results)
        successful = sum(1 for r in results if r["success"])
        failed = total - successful
        
        summary = {
            "total_commands": total,
            "successful": successful,
            "failed": failed,
            "success_rate": successful / total if total > 0 else 0.0,
            "failed_commands": [r["command"] for r in results if not r["success"]]
        }
        
        return summary
