## 创建子网

```shell
# 设置VLAN ID，用于标识不同的虚拟局域网
export VLAN_ID=1003
# 设置子网IP地址的前缀
export PRE_GA_WAY_IP=10.50.101
# 设置子网的网关IP地址
export ZW_GA_WAY_IP=***********
# 设置子网掩码位数
export ZW_MASK=24
# 设置用于监控的IP地址
export MON_IP=************
# 设置监控管理服务器的IP地址
export MON_M_IP=*************
# 设置监控管理服务器的端口
export MON_M_PORT=30428

# 创建一个专门用于此VLAN的网络命名空间，以隔离网络环境
ip netns add ns-vlan${VLAN_ID}

# 创建一对虚拟以太网设备（veth pair），用于连接主机和网络命名空间
ip link add v-lan-host-${VLAN_ID} type veth peer name v-lan-ns-${VLAN_ID}
# 将veth pair的一端（v-lan-ns-）移动到新创建的网络命名空间中
ip link set v-lan-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
# 在网络命名空间内，为veth设备配置IP地址，作为该VLAN子网的网关
ip netns exec ns-vlan${VLAN_ID} ip addr add ${ZW_GA_WAY_IP}/24 dev v-lan-ns-${VLAN_ID}
# 将veth pair在主机侧的一端（v-lan-host-）添加到OVS网桥br-vlan，并打上VLAN标签
ovs-vsctl add-port br-vlan v-lan-host-${VLAN_ID}  tag=${VLAN_ID}

# 创建另一对veth pair，用于弹性IP（EIP）的连接
ip link add v-eip-host-${VLAN_ID} type veth peer name v-eip-ns-${VLAN_ID}
# 将EIP veth pair的一端移动到VLAN的网络命名空间中
ip link set v-eip-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
# 将EIP veth pair在主机侧的一端添加到OVS网桥br-eip
ovs-vsctl add-port br-eip v-eip-host-${VLAN_ID}

# 创建用于监控的veth pair
ip link add v-mon-host-${VLAN_ID} type veth peer name v-mon-ns-${VLAN_ID}
# 将监控veth pair的一端移动到VLAN的网络命名空间中
ip link set v-mon-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
# 在网络命名空间内为监控veth设备配置IP地址
ip netns exec ns-vlan${VLAN_ID} ip addr add ${MON_IP}/24 dev v-mon-ns-${VLAN_ID}
# 将监控veth pair在主机侧的一端添加到OVS网桥br-mon
ovs-vsctl add-port br-mon v-mon-host-${VLAN_ID}

#-------------- 统一启动网络接口
# 启动主机侧的veth接口
ip link set v-lan-host-${VLAN_ID} up
# 启动网络命名空间内的veth接口
ip netns exec ns-vlan${VLAN_ID} ip link set v-lan-ns-${VLAN_ID} up
# 启动EIP veth pair的两端接口
ip link set v-eip-host-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set v-eip-ns-${VLAN_ID} up
# 启动监控veth pair的两端接口
ip link set v-mon-host-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set v-mon-ns-${VLAN_ID} up
# 启动网络命名空间内的本地环回接口
ip netns exec ns-vlan${VLAN_ID} ip link set lo up

# 在命名空间内添加一条nftables规则，对发往同一子网的流量进行源地址转换（SNAT），将其源IP伪装成网关IP
ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip saddr ${PRE_GA_WAY_IP}.0/24 ip daddr ${PRE_GA_WAY_IP}.0/${ZW_MASK} counter snat to ${ZW_GA_WAY_IP}
# 在命名空间内创建nftables的nat表和PREROUTING链，用于处理入站流量的目的地址转换
ip netns exec ns-vlan${VLAN_ID} nft add chain ip nat PREROUTING { type nat hook prerouting priority -100 \; }
# 添加DNAT规则，将对特定IP（***************，通常用于云元数据服务）443端口的访问重定向到内部的监控管理服务
ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to ${MON_M_IP}:${MON_M_PORT}
# 添加SNAT规则，将访问监控管理服务的出站流量的源IP伪装成监控IP
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 30428 counter snat to ************
# 允许访问监控管理服务的69端口（TFTP）和9879端口，同样进行SNAT
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 69 counter snat to ************
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 9879 counter snat to ************

# 显示网络命名空间内的IP地址配置，以供验证
ip netns exec ns-vlan${VLAN_ID} ip addr
```

## 挂载EIP
```shell
# 设定要操作的VLAN ID
export VLAN_ID=1003
# 设定EIP的IP地址前缀
export EIP_PRE=192.168.201
# 组合成完整的EIP地址
export EIP=${EIP_PRE}.33
# 设定EIP所在网络的网关IP
export EIP_GA_WAY_IP=${EIP_PRE}.1
# 设定内网中需要绑定EIP的服务器IP地址
export ZW_IP=***********

# 在VLAN的网络命名空间内，为EIP的veth接口配置EIP地址
ip netns exec ns-vlan${VLAN_ID} ip addr add ${EIP}/24 dev v-eip-ns-${VLAN_ID}
# 在命名空间内添加默认路由，使得所有出站流量都通过EIP的网关出去
ip netns exec ns-vlan${VLAN_ID} ip route add default via ${EIP_GA_WAY_IP} dev v-eip-ns-${VLAN_ID}

# 在命名空间内创建nftables的nat表（如果尚不存在）
ip netns exec ns-vlan${VLAN_ID} nft add table ip nat
# 添加DNAT规则，将所有访问该EIP的入站流量，都转发给内网的指定服务器
ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr ${EIP} counter dnat to ${ZW_IP}
# 添加SNAT规则，将内网服务器发出的、且目的地不为私有网段的流量，其源IP地址伪装成EIP地址
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING \
    ip saddr ${ZW_IP} \
    ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } \
    snat to ${EIP}

# 列出命名空间内nat表的所有规则，用于检查
ip netns exec ns-vlan${VLAN_ID}  nft list table ip nat
# 删除脚本参考：以下命令用于查看规则以便手动删除
# 使用iptables（旧版工具）列出nat表规则，并显示行号
ip netns exec ns-vlan${VLAN_ID} iptables -t nat -L -n --line-numbers
# 使用nftables列出nat表规则，并显示句柄（handle），方便通过句柄删除规则
ip netns exec ns-vlan${VLAN_ID}  nft -a list table ip nat
```

# 租户子网配置样例
```json
{
  "tenant_id": "tenant-1",
  "vlan_id": 1003,
  "subnet": {
    "gateway_ip": "***********",
    "prefix": "10.50.101",
    "mask": 24,
    "cidr": "***********/24"
  },
  "monitoring": {
    "ip": "************",
    "management_server": {
      "ip": "*************",
      "port": 30428
    }
  },
  "eips": [
    {
      "ip": "**************",
      "gateway_ip": "*************",
      "prefix": "192.168.201",
      "mask": 24,
      "internal_ip": "***********"
    }
  ],
  "nat_rules": {
    "metadata_service": {
      "target_ip": "***************",
      "target_port": 443,
      "redirect_to_ip": "*************",
      "redirect_to_port": 30428
    },
    "monitoring_snat": {
      "destination_ip": "*************",
      "ports": [30428, 69, 9879],
      "source_nat_ip": "************"
    },
    "subnet_snat": {
      "source_cidr": "***********/24",
      "destination_cidr": "***********/24",
      "nat_to_ip": "***********"
    },
    "eip_rules": [
      {
        "eip": "**************",
        "internal_ip": "***********",
        "exclude_private_ranges": [
          "10.0.0.0/8",
          "**********/12",
          "***********/24",
          "***********/24"
        ]
      }
    ]
  },
  "network_interfaces": {
    "vlan_veth": {
      "host_side": "v-lan-host-1003",
      "ns_side": "v-lan-ns-1003"
    },
    "eip_veth": {
      "host_side": "v-eip-host-1003",
      "ns_side": "v-eip-ns-1003"
    },
    "monitoring_veth": {
      "host_side": "v-mon-host-1003",
      "ns_side": "v-mon-ns-1003"
    }
  },
  "ovs_bridges": {
    "vlan_bridge": "br-vlan",
    "eip_bridge": "br-eip",
    "monitoring_bridge": "br-mon"
  },
  "namespace": "ns-vlan1003"
}
```