## api server 模块设计

提供外部访问的api接口, 接收外部请求, 调用 core service 模块的业务逻辑处理;

### 业务功能
##### 创建子网
  参数：tenant_id, vlan_id, subnet_gw_ip
  调用 core service 模块的创建子网网关逻辑;

##### 挂载EIP
  参数：tenant_id, vlan_id, eip, internal_ip
  调用 core service 模块的挂载EIP逻辑;

## core service 模块设计
core service 模块是整个系统的业务逻辑核心, 负责处理所有的业务逻辑

### 业务功能

#### 初始化 etcd 连接
  启动服务时加载配置文件, 根据etcd配置连接到 etcd
server_config 配置文件
```yaml
server:
  port: 30090
  shard_count: 32
etcd:
  host: localhost
  port: 2379
  timeout: 5.0
  username: ''
  password: ''
logging:
  level: INFO
  file: ''
```

#### 初始元数据
  连接etcd成功后, 检查 /network/server/metadata 是否存在, 如果不存在则创建, 包含以下数据:

#### agent注册处理
  监听ctcd 的 /network/agent/register/ 目录, 接收到事件时执行处理逻辑;

  DELETE 事件: 修改 /network/server/metadata 数据, 对应 agent 的 online 字段设置为 false, offline_time 设置为当前时间; 
  如果 shard 字段不为空, 则将对应 agent 绑定的 shard 重新分配给其他 online 状态的 agent, 并将 shard 字段设置为空;

  PUT 事件: 根据 prev_kv 判断 status 字段变化, 如果是 running -> running, 则不处理;
  如果是 running -> error, 执行逻辑同 DELETE 事件;
  如果是 error -> running 或 null -> running, 则修改 /network/server/metadata 数据, 对应 agent 的 online 字段设置为 true, offline_time 设置为 0;

/network/server/metadata
```json
{
  "server":{
      "shard_count": 32
  },
  "agent":[
    {
      "online": true,
      "agent_id":"agent-1",
      "offline_time": 0,
      "shard":["s1"]
    },
    {
      "online": true,
      "agent_id":"agent-2",
      "offline_time": 0,
      "shard":["s2"]
    }
  ]
}
```

#### 创建子网
  参数: tenant_id, vlan_id, subnet_gw_ip
  
  1.检查 vlan_id 是否在允许的范围内, 如果不在范围内, 则返回错误;
  2.检查etcd中key: /network/server/tenant/{tenant_id}/subnet-vlan-{vlan_id} 是否存在, 如果存在则返回错误;
  3.创建subnet数据, 包含以下字段:
  ```json
  {
    "tenant_id": "tenant-1",
    "vlan_id": 1000,
    "subnet_gw_ip": "*************",
    "netns": [
      "ns-vlan1000"
    ],
    "v-switch": {
      "br-vlan": {
        "type": "veth",
        "ports": []
      },
      "br-eip": {
        "type": "veth",
        "ports": []
      },
      "br-mon": {
        "type": "veth",
        "ports": []
      }
    },
    "eips": [
      {
        "eip": "***********",
        "internal_ip": "*************0"
      }
    ],
    "route_table": [],
    "nat_table": {
      "table_name": "nat",
      "chains": {
        "prerouting": {
          "type": "nat",
          "rules": []
        },
        "postrouting": {
          "type": "nat",
          "rules": []
        }
      }
    }
  }
  ```
  创建指令模板
  ```bash
  # 创建一个专门用于此VLAN的网络命名空间，以隔离网络环境
  ip netns add ns-vlan${VLAN_ID}

  # 创建一对虚拟以太网设备（veth pair），用于连接主机和网络命名空间
  ip link add v-lan-host-${VLAN_ID} type veth peer name v-lan-ns-${VLAN_ID}
  # 将veth pair的一端（v-lan-ns-）移动到新创建的网络命名空间中
  ip link set v-lan-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 在网络命名空间内，为veth设备配置IP地址，作为该VLAN子网的网关
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${ZW_GA_WAY_IP}/24 dev v-lan-ns-${VLAN_ID}
  # 将veth pair在主机侧的一端（v-lan-host-）添加到OVS网桥br-vlan，并打上VLAN标签
  ovs-vsctl add-port br-vlan v-lan-host-${VLAN_ID}  tag=${VLAN_ID}

  # 创建另一对veth pair，用于弹性IP（EIP）的连接
  ip link add v-eip-host-${VLAN_ID} type veth peer name v-eip-ns-${VLAN_ID}
  # 将EIP veth pair的一端移动到VLAN的网络命名空间中
  ip link set v-eip-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 将EIP veth pair在主机侧的一端添加到OVS网桥br-eip
  ovs-vsctl add-port br-eip v-eip-host-${VLAN_ID}

  # 创建用于监控的veth pair
  ip link add v-mon-host-${VLAN_ID} type veth peer name v-mon-ns-${VLAN_ID}
  # 将监控veth pair的一端移动到VLAN的网络命名空间中
  ip link set v-mon-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 在网络命名空间内为监控veth设备配置IP地址
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${MON_IP}/24 dev v-mon-ns-${VLAN_ID}
  # 将监控veth pair在主机侧的一端添加到OVS网桥br-mon
  ovs-vsctl add-port br-mon v-mon-host-${VLAN_ID}

  #-------------- 统一启动网络接口
  # 启动主机侧的veth接口
  ip link set v-lan-host-${VLAN_ID} up
  # 启动网络命名空间内的veth接口
  ip netns exec ns-vlan${VLAN_ID} ip link set v-lan-ns-${VLAN_ID} up
  # 启动EIP veth pair的两端接口
  ip link set v-eip-host-${VLAN_ID} up
  ip netns exec ns-vlan${VLAN_ID} ip link set v-eip-ns-${VLAN_ID} up
  # 启动监控veth pair的两端接口
  ip link set v-mon-host-${VLAN_ID} up
  ip netns exec ns-vlan${VLAN_ID} ip link set v-mon-ns-${VLAN_ID} up
  # 启动网络命名空间内的本地环回接口
  ip netns exec ns-vlan${VLAN_ID} ip link set lo up

  # 在命名空间内添加一条nftables规则，对发往同一子网的流量进行源地址转换（SNAT），将其源IP伪装成网关IP
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip saddr ${PRE_GA_WAY_IP}.0/24 ip daddr ${PRE_GA_WAY_IP}.0/${ZW_MASK} counter snat to ${ZW_GA_WAY_IP}
  # 在命名空间内创建nftables的nat表和PREROUTING链，用于处理入站流量的目的地址转换
  ip netns exec ns-vlan${VLAN_ID} nft add chain ip nat PREROUTING { type nat hook prerouting priority -100 \; }
  # 添加DNAT规则，将对特定IP（***************，通常用于云元数据服务）443端口的访问重定向到内部的监控管理服务
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to ${MON_M_IP}:${MON_M_PORT}
  # 添加SNAT规则，将访问监控管理服务的出站流量的源IP伪装成监控IP
  ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 30428 counter snat to ************
  # 允许访问监控管理服务的69端口（TFTP）和9879端口，同样进行SNAT
  ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 69 counter snat to ************
  ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 9879 counter snat to ************

  # 显示网络命名空间内的IP地址配置，以供验证
  ip netns exec ns-vlan${VLAN_ID} ip addr
  ```


#### 挂载EIP
  参数: tenant_id, vlan_id, eip, gateway_ip, internal_ip
  根据指令模板生成指令配置, 基于tenant_id进行取模计算, 获取对应的 shard, 并写入 /network/server/instruct/{shard}/{id}

 /network/server/instruct/{shard}/{id}
```json
{
  "type":"create_gateway",
  "tenant_id":"....",
  "request_id":"333",
  "cmds":[
    "echo 1",
    "echo 2"
  ],
  "revocation":[
    "echo u1",
    "echo u2"
  ]
}
```

创建指令模板
```bash
# 在VLAN的网络命名空间内，为EIP的veth接口配置EIP地址
ip netns exec ns-vlan${VLAN_ID} ip addr add ${EIP}/24 dev v-eip-ns-${VLAN_ID}
# 在命名空间内添加默认路由，使得所有出站流量都通过EIP的网关出去
ip netns exec ns-vlan${VLAN_ID} ip route add default via ${EIP_GA_WAY_IP} dev v-eip-ns-${VLAN_ID}

# 在命名空间内创建nftables的nat表（如果尚不存在）
ip netns exec ns-vlan${VLAN_ID} nft add table ip nat
# 添加DNAT规则，将所有访问该EIP的入站流量，都转发给内网的指定服务器
ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr ${EIP} counter dnat to ${ZW_IP}
# 添加SNAT规则，将内网服务器发出的、且目的地不为私有网段的流量，其源IP地址伪装成EIP地址
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING \
    ip saddr ${ZW_IP} \
    ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } \
    snat to ${EIP}

# 列出命名空间内nat表的所有规则，用于检查
ip netns exec ns-vlan${VLAN_ID}  nft list table ip nat
# 删除脚本参考：以下命令用于查看规则以便手动删除
# 使用iptables（旧版工具）列出nat表规则，并显示行号
ip netns exec ns-vlan${VLAN_ID} iptables -t nat -L -n --line-numbers
# 使用nftables列出nat表规则，并显示句柄（handle），方便通过句柄删除规则
ip netns exec ns-vlan${VLAN_ID}  nft -a list table ip nat
```

## agent 模块设计
agent 模块是系统的执行单元, 负责执行指令, 处理网络配置等;

### 业务功能
#### 加载配置
  启动agent时加载配置文件, 根据etcd配置连接到 etcd
agent_config 配置文件
```yaml
agent:
  agent_id: agent-1
  check_env: true
  heartbeat_interval: 30.0
  max_retries: 3
etcd:
  host: localhost
  port: 2379
  timeout: 5.0
  username: ''
  password: ''
logging:
  level: INFO
  file: ''
```

#### agent注册
  启动时, 初始化 heartbeat 线程, 根据 heartbeat_interval 配置定时执行;

  判断 check_env 配置, 如果为 true, 则执行环境检查, 如果检查不通过, 则设置 status 为 error, 检查通或check_env配置为false, 过则设置为 running;

#### 环境检查
  检查 ovs是否安装, 检查ip转发配置:`net.ipv4.ip_forward = 1`,`net.ipv4.conf.all.rp_filter = 0`,`net.ipv4.conf.default.rp_filter = 0`

  根据 heartbeat_interval 配置定时向etcd更新 key: /network/agent/register/{agent_id} , key 绑定租约, 租约时间设置为 heartbeat_interval 的 3 倍;

/network/agent/register/{agent_id} 
```json
{
  "status":"running | error",
  "agent_id": "agent-1",
  "lase_heartbeat_time": 1751526218187
}
```

#### 指令执行
##### 元数据监听
  启动时, 监听 etcd /network/server/metadata 的值, 读取当前 agent 的 shard 信息;
##### 分片同步
  当 shard 信息变化时, 检查 /network/agent/task/{agent_id} 目录, 如果分片不一致, 则根据 instruct.revocation 执行撤销命令;
##### 任务执行
  根据获取的分片列表, 监听etcd /network/server/instruct/{shard} 目录, 接收到 DELETE事件: 读取revocation字段, 获取命令列表执行撤销命令; 接收到 PUT 事件时判断是新增还是更新, 新增则读取cmds字段获取命令列表安顺序执行, 更新则先执行撤销命令, 在执行新增命令; 
  每次执行命令前, 先在 /network/agent/task/{agent_id}/{shard}/{id} 创建任务数据, status 设置为 exec, 执行命令结束后, 如果执行成功, 则设置为 ok, 失败则设置为 err;


/network/agent/task/{agent_id}/{shard}/{id}
```json
{
  "instruct":{
    "type":"create_gateway",
    "request_id":"333",
    "cmds":[
      "echo 1",
      "echo 2"
    ],
    "revocation":[
      "echo u1",
      "echo u2"
    ]
  },
  "status":"exec | ok | err",
  "message":""
}
```

