#!/usr/bin/env python3
"""
Test script to verify environment variable override functionality.
"""

import os
import sys
import tempfile
import yaml

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from v_switch.config.server_config import ServerConfig
from v_switch.config.agent_config import AgentConfig


def test_server_config_env_override():
    """Test server configuration environment variable override."""
    print("Testing Server Configuration Environment Override")
    print("-" * 50)
    
    # Create a temporary config file
    config_data = {
        'server': {
            'port': 30090,
            'shard_count': 32
        },
        'etcd': {
            'host': 'localhost',
            'port': 2379,
            'timeout': 5.0
        },
        'logging': {
            'level': 'INFO',
            'file': ''
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(config_data, f)
        config_file = f.name
    
    try:
        # Test without environment variables
        print("1. Configuration from file only:")
        config1 = ServerConfig(config_file)
        print(f"   Server port: {config1.server.port}")
        print(f"   ETCD host: {config1.etcd.host}")
        print(f"   ETCD port: {config1.etcd.port}")
        print(f"   Logging level: {config1.logging.level}")
        print()
        
        # Set environment variables
        os.environ['VSWITCH_SERVER_PORT'] = '8080'
        os.environ['VSWITCH_ETCD_HOST'] = '*************'
        os.environ['VSWITCH_ETCD_PORT'] = '30379'
        os.environ['VSWITCH_LOGGING_LEVEL'] = 'DEBUG'
        os.environ['VSWITCH_SERVER_SHARD_COUNT'] = '64'
        
        print("2. Configuration with environment variable overrides:")
        print("   Environment variables set:")
        print("   - VSWITCH_SERVER_PORT=8080")
        print("   - VSWITCH_ETCD_HOST=*************")
        print("   - VSWITCH_ETCD_PORT=30379")
        print("   - VSWITCH_LOGGING_LEVEL=DEBUG")
        print("   - VSWITCH_SERVER_SHARD_COUNT=64")
        print()
        
        config2 = ServerConfig(config_file)
        print(f"   Server port: {config2.server.port} (should be 8080)")
        print(f"   Server shard count: {config2.server.shard_count} (should be 64)")
        print(f"   ETCD host: {config2.etcd.host} (should be *************)")
        print(f"   ETCD port: {config2.etcd.port} (should be 30379)")
        print(f"   Logging level: {config2.logging.level} (should be DEBUG)")
        print()
        
        # Verify the overrides worked
        assert config2.server.port == 8080, f"Expected port 8080, got {config2.server.port}"
        assert config2.server.shard_count == 64, f"Expected shard_count 64, got {config2.server.shard_count}"
        assert config2.etcd.host == '*************', f"Expected host *************, got {config2.etcd.host}"
        assert config2.etcd.port == 30379, f"Expected port 30379, got {config2.etcd.port}"
        assert config2.logging.level == 'DEBUG', f"Expected level DEBUG, got {config2.logging.level}"
        
        print("✓ Server configuration environment override test PASSED")
        
    finally:
        # Clean up
        os.unlink(config_file)
        for env_var in ['VSWITCH_SERVER_PORT', 'VSWITCH_ETCD_HOST', 'VSWITCH_ETCD_PORT', 
                       'VSWITCH_LOGGING_LEVEL', 'VSWITCH_SERVER_SHARD_COUNT']:
            if env_var in os.environ:
                del os.environ[env_var]


def test_agent_config_env_override():
    """Test agent configuration environment variable override."""
    print("\nTesting Agent Configuration Environment Override")
    print("-" * 50)
    
    # Create a temporary config file
    config_data = {
        'agent': {
            'agent_id': 'default-agent',
            'check_env': True,
            'heartbeat_interval': 30.0,
            'max_retries': 3
        },
        'etcd': {
            'host': 'localhost',
            'port': 2379
        },
        'logging': {
            'level': 'INFO'
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(config_data, f)
        config_file = f.name
    
    try:
        # Test without environment variables
        print("1. Configuration from file only:")
        config1 = AgentConfig(config_file)
        print(f"   Agent ID: {config1.agent.agent_id}")
        print(f"   Check env: {config1.agent.check_env}")
        print(f"   Heartbeat interval: {config1.agent.heartbeat_interval}")
        print(f"   Max retries: {config1.agent.max_retries}")
        print()
        
        # Set environment variables
        os.environ['VSWITCH_AGENT_ID'] = 'test-agent-001'
        os.environ['VSWITCH_AGENT_CHECK_ENV'] = 'false'
        os.environ['VSWITCH_AGENT_HEARTBEAT_INTERVAL'] = '60.0'
        os.environ['VSWITCH_AGENT_MAX_RETRIES'] = '5'
        os.environ['VSWITCH_ETCD_HOST'] = '*************'
        
        print("2. Configuration with environment variable overrides:")
        print("   Environment variables set:")
        print("   - VSWITCH_AGENT_ID=test-agent-001")
        print("   - VSWITCH_AGENT_CHECK_ENV=false")
        print("   - VSWITCH_AGENT_HEARTBEAT_INTERVAL=60.0")
        print("   - VSWITCH_AGENT_MAX_RETRIES=5")
        print("   - VSWITCH_ETCD_HOST=*************")
        print()
        
        config2 = AgentConfig(config_file)
        print(f"   Agent ID: {config2.agent.agent_id} (should be test-agent-001)")
        print(f"   Check env: {config2.agent.check_env} (should be False)")
        print(f"   Heartbeat interval: {config2.agent.heartbeat_interval} (should be 60.0)")
        print(f"   Max retries: {config2.agent.max_retries} (should be 5)")
        print(f"   ETCD host: {config2.etcd.host} (should be *************)")
        print()
        
        # Verify the overrides worked
        assert config2.agent.agent_id == 'test-agent-001', f"Expected agent_id test-agent-001, got {config2.agent.agent_id}"
        assert config2.agent.check_env == False, f"Expected check_env False, got {config2.agent.check_env}"
        assert config2.agent.heartbeat_interval == 60.0, f"Expected heartbeat_interval 60.0, got {config2.agent.heartbeat_interval}"
        assert config2.agent.max_retries == 5, f"Expected max_retries 5, got {config2.agent.max_retries}"
        assert config2.etcd.host == '*************', f"Expected host *************, got {config2.etcd.host}"
        
        print("✓ Agent configuration environment override test PASSED")
        
    finally:
        # Clean up
        os.unlink(config_file)
        for env_var in ['VSWITCH_AGENT_ID', 'VSWITCH_AGENT_CHECK_ENV', 'VSWITCH_AGENT_HEARTBEAT_INTERVAL', 
                       'VSWITCH_AGENT_MAX_RETRIES', 'VSWITCH_ETCD_HOST']:
            if env_var in os.environ:
                del os.environ[env_var]


def test_invalid_env_values():
    """Test handling of invalid environment variable values."""
    print("\nTesting Invalid Environment Variable Values")
    print("-" * 50)
    
    # Set invalid environment variables
    os.environ['VSWITCH_SERVER_PORT'] = 'invalid_port'
    os.environ['VSWITCH_AGENT_CHECK_ENV'] = 'maybe'
    
    try:
        print("Setting invalid environment variables:")
        print("- VSWITCH_SERVER_PORT=invalid_port")
        print("- VSWITCH_AGENT_CHECK_ENV=maybe")
        print()
        
        # This should handle invalid values gracefully
        config = ServerConfig()
        print("✓ Invalid environment variables handled gracefully")
        
    finally:
        # Clean up
        for env_var in ['VSWITCH_SERVER_PORT', 'VSWITCH_AGENT_CHECK_ENV']:
            if env_var in os.environ:
                del os.environ[env_var]


def main():
    """Main function to run all tests."""
    print("Environment Variable Override Tests")
    print("=" * 50)
    
    try:
        test_server_config_env_override()
        test_agent_config_env_override()
        test_invalid_env_values()
        
        print("\n" + "=" * 50)
        print("✓ All tests PASSED!")
        return 0
        
    except Exception as e:
        print(f"\n✗ Test FAILED: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
