#!/usr/bin/env python3
"""
Test script to verify environment variable override in real configuration loading.
"""

import os
import sys

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from v_switch.config.server_config import ServerConfig
from v_switch.config.agent_config import Agent<PERSON>onfig


def test_real_server_config():
    """Test server configuration with real config file."""
    print("Testing Real Server Configuration")
    print("-" * 40)
    
    config_file = "config/server_config.yaml"
    
    # Test without environment variables
    print("1. Loading configuration from file only:")
    config1 = ServerConfig(config_file)
    print(f"   Server port: {config1.server.port}")
    print(f"   Server shard count: {config1.server.shard_count}")
    print(f"   ETCD host: {config1.etcd.host}")
    print(f"   ETCD port: {config1.etcd.port}")
    print(f"   Logging level: {config1.logging.level}")
    print()
    
    # Set environment variables
    os.environ['VSWITCH_SERVER_PORT'] = '8080'
    os.environ['VSWITCH_ETCD_HOST'] = '127.0.0.1'
    os.environ['VSWITCH_LOGGING_LEVEL'] = 'DEBUG'
    
    print("2. Loading configuration with environment overrides:")
    print("   Environment variables:")
    print("   - VSWITCH_SERVER_PORT=8080")
    print("   - VSWITCH_ETCD_HOST=127.0.0.1")
    print("   - VSWITCH_LOGGING_LEVEL=DEBUG")
    print()
    
    config2 = ServerConfig(config_file)
    print(f"   Server port: {config2.server.port} (overridden)")
    print(f"   Server shard count: {config2.server.shard_count} (from file)")
    print(f"   ETCD host: {config2.etcd.host} (overridden)")
    print(f"   ETCD port: {config2.etcd.port} (from file)")
    print(f"   Logging level: {config2.logging.level} (overridden)")
    print()
    
    # Verify overrides
    assert config2.server.port == 8080, f"Expected port 8080, got {config2.server.port}"
    assert config2.etcd.host == '127.0.0.1', f"Expected host 127.0.0.1, got {config2.etcd.host}"
    assert config2.logging.level == 'DEBUG', f"Expected level DEBUG, got {config2.logging.level}"
    
    # Verify non-overridden values remain from file
    assert config2.server.shard_count == config1.server.shard_count, "Shard count should remain from file"
    assert config2.etcd.port == config1.etcd.port, "ETCD port should remain from file"
    
    print("✓ Real server configuration test PASSED")
    
    # Clean up
    for env_var in ['VSWITCH_SERVER_PORT', 'VSWITCH_ETCD_HOST', 'VSWITCH_LOGGING_LEVEL']:
        if env_var in os.environ:
            del os.environ[env_var]


def test_real_agent_config():
    """Test agent configuration with real config file."""
    print("\nTesting Real Agent Configuration")
    print("-" * 40)
    
    config_file = "config/agent_config.yaml"
    
    # Test without environment variables
    print("1. Loading configuration from file only:")
    try:
        config1 = AgentConfig(config_file)
        print(f"   Agent ID: {config1.agent.agent_id}")
        print(f"   Check env: {config1.agent.check_env}")
        print(f"   Heartbeat interval: {config1.agent.heartbeat_interval}")
        print(f"   ETCD host: {config1.etcd.host}")
        print()
        
        # Set environment variables
        os.environ['VSWITCH_AGENT_ID'] = 'test-agent-env'
        os.environ['VSWITCH_AGENT_HEARTBEAT_INTERVAL'] = '45.0'
        os.environ['VSWITCH_ETCD_HOST'] = '************'
        
        print("2. Loading configuration with environment overrides:")
        print("   Environment variables:")
        print("   - VSWITCH_AGENT_ID=test-agent-env")
        print("   - VSWITCH_AGENT_HEARTBEAT_INTERVAL=45.0")
        print("   - VSWITCH_ETCD_HOST=************")
        print()
        
        config2 = AgentConfig(config_file)
        print(f"   Agent ID: {config2.agent.agent_id} (overridden)")
        print(f"   Check env: {config2.agent.check_env} (from file)")
        print(f"   Heartbeat interval: {config2.agent.heartbeat_interval} (overridden)")
        print(f"   ETCD host: {config2.etcd.host} (overridden)")
        print()
        
        # Verify overrides
        assert config2.agent.agent_id == 'test-agent-env', f"Expected agent_id test-agent-env, got {config2.agent.agent_id}"
        assert config2.agent.heartbeat_interval == 45.0, f"Expected heartbeat_interval 45.0, got {config2.agent.heartbeat_interval}"
        assert config2.etcd.host == '************', f"Expected host ************, got {config2.etcd.host}"
        
        print("✓ Real agent configuration test PASSED")
        
    except FileNotFoundError:
        print("   Agent config file not found, creating minimal test...")
        # Test with no config file
        os.environ['VSWITCH_AGENT_ID'] = 'test-agent-env-only'
        os.environ['VSWITCH_ETCD_HOST'] = '************'
        
        config = AgentConfig()
        print(f"   Agent ID: {config.agent.agent_id} (from env)")
        print(f"   ETCD host: {config.etcd.host} (from env)")
        
        assert config.agent.agent_id == 'test-agent-env-only'
        assert config.etcd.host == '************'
        
        print("✓ Agent configuration (env only) test PASSED")
    
    # Clean up
    for env_var in ['VSWITCH_AGENT_ID', 'VSWITCH_AGENT_HEARTBEAT_INTERVAL', 'VSWITCH_ETCD_HOST']:
        if env_var in os.environ:
            del os.environ[env_var]


def test_configuration_to_dict():
    """Test configuration serialization with environment overrides."""
    print("\nTesting Configuration Serialization")
    print("-" * 40)
    
    # Set some environment variables
    os.environ['VSWITCH_SERVER_PORT'] = '9090'
    os.environ['VSWITCH_ETCD_HOST'] = 'test-etcd'
    os.environ['VSWITCH_LOGGING_LEVEL'] = 'WARNING'
    
    config = ServerConfig("config/server_config.yaml")
    config_dict = config.to_dict()
    
    print("Configuration as dictionary:")
    print(f"   server.port: {config_dict['server']['port']}")
    print(f"   etcd.host: {config_dict['etcd']['host']}")
    print(f"   logging.level: {config_dict['logging']['level']}")
    
    # Verify the dictionary contains overridden values
    assert config_dict['server']['port'] == 9090
    assert config_dict['etcd']['host'] == 'test-etcd'
    assert config_dict['logging']['level'] == 'WARNING'
    
    print("✓ Configuration serialization test PASSED")
    
    # Clean up
    for env_var in ['VSWITCH_SERVER_PORT', 'VSWITCH_ETCD_HOST', 'VSWITCH_LOGGING_LEVEL']:
        if env_var in os.environ:
            del os.environ[env_var]


def main():
    """Main function to run all tests."""
    print("Real Configuration Environment Override Tests")
    print("=" * 50)
    
    try:
        test_real_server_config()
        test_real_agent_config()
        test_configuration_to_dict()
        
        print("\n" + "=" * 50)
        print("✓ All real configuration tests PASSED!")
        return 0
        
    except Exception as e:
        print(f"\n✗ Test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
