#!/usr/bin/env python3
"""
Test script for the new delete API endpoints.
"""

import argparse
import json
import requests
import time
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_delete_test(base_url: str, tenant_id: str) -> str:
    """Test the delete test endpoint."""
    print("Testing DELETE /network/test...")
    
    url = f"{base_url}/network/test"
    data = {
        "tenant_id": tenant_id
    }
    
    try:
        response = requests.delete(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                request_id = result.get("request_id")
                print(f"✓ Delete test request submitted successfully. Request ID: {request_id}")
                return request_id
            else:
                print(f"✗ Delete test request failed: {result}")
        else:
            print(f"✗ Delete test request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error testing delete test: {e}")
    
    return None


def test_delete_subnet_gateway(base_url: str, tenant_id: str, vlan_id: str) -> str:
    """Test the delete subnet gateway endpoint."""
    print("Testing DELETE /network/subnet-gateway...")
    
    url = f"{base_url}/network/subnet-gateway"
    data = {
        "tenant_id": tenant_id,
        "vlan_id": int(vlan_id)
    }
    
    try:
        response = requests.delete(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                request_id = result.get("request_id")
                print(f"✓ Delete subnet gateway request submitted successfully. Request ID: {request_id}")
                return request_id
            else:
                print(f"✗ Delete subnet gateway request failed: {result}")
        else:
            print(f"✗ Delete subnet gateway request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error testing delete subnet gateway: {e}")
    
    return None


def test_delete_eip(base_url: str, tenant_id: str, vlan_id: str, eip: str, internal_ip: str) -> str:
    """Test the delete EIP endpoint."""
    print("Testing DELETE /network/eip...")
    
    url = f"{base_url}/network/eip"
    data = {
        "tenant_id": tenant_id,
        "vlan_id": int(vlan_id),
        "eip": eip,
        "internal_ip": internal_ip
    }
    
    try:
        response = requests.delete(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                request_id = result.get("request_id")
                print(f"✓ Delete EIP request submitted successfully. Request ID: {request_id}")
                return request_id
            else:
                print(f"✗ Delete EIP request failed: {result}")
        else:
            print(f"✗ Delete EIP request failed with status {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error testing delete EIP: {e}")
    
    return None


def test_get_instruction_status(base_url: str, request_id: str, tenant_id: str):
    """Test getting instruction status."""
    print(f"Testing GET /instruction/{request_id}...")
    
    url = f"{base_url}/instruction/{request_id}"
    params = {"tenant_id": tenant_id}
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✓ Instruction status retrieved successfully")
                instruction = result.get("instruction", {})
                print(f"  Type: {instruction.get('type', 'N/A')}")
                print(f"  Status: {instruction.get('status', 'N/A')}")
            else:
                print(f"✗ Failed to get instruction status: {result}")
        else:
            print(f"✗ Failed to get instruction status with status {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error getting instruction status: {e}")


def test_health_check(base_url: str):
    """Test the health check endpoint."""
    print("Testing GET /health...")
    
    url = f"{base_url}/health"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✓ Health check passed")
                return True
            else:
                print(f"✗ Health check failed: {result}")
        else:
            print(f"✗ Health check failed with status {response.status_code}")
            
    except Exception as e:
        print(f"✗ Error in health check: {e}")
    
    return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test delete API endpoints")
    parser.add_argument("--host", default="localhost", help="API server host")
    parser.add_argument("--port", type=int, default=8080, help="API server port")
    parser.add_argument("--tenant-id", default="test-tenant-123", help="Tenant ID for testing")
    parser.add_argument("--vlan-id", default="100", help="VLAN ID for testing")
    parser.add_argument("--eip", default="**********", help="EIP for testing")
    parser.add_argument("--internal-ip", default="**************", help="Internal IP for testing")
    
    args = parser.parse_args()
    
    base_url = f"http://{args.host}:{args.port}"
    
    print(f"Testing delete API endpoints at {base_url}")
    print("=" * 50)
    
    # Test health check first
    if not test_health_check(base_url):
        print("Health check failed. Make sure the API server is running.")
        return 1
    
    print()
    
    # Test delete operations
    print("Testing delete operations...")
    print("-" * 30)
    
    # Test delete test
    test_request_id = test_delete_test(base_url, args.tenant_id)
    print()
    
    # Test delete subnet gateway
    subnet_request_id = test_delete_subnet_gateway(
        base_url, 
        args.tenant_id, 
        args.vlan_id
    )
    print()
    
    # Test delete EIP
    eip_request_id = test_delete_eip(
        base_url,
        args.tenant_id,
        args.vlan_id,
        args.eip,
        args.internal_ip
    )
    print()
    
    # Wait a bit for processing
    if test_request_id or subnet_request_id or eip_request_id:
        print("Waiting 2 seconds for processing...")
        time.sleep(2)
        print()
    
    # Test instruction status for delete operations
    if test_request_id:
        test_get_instruction_status(base_url, test_request_id, args.tenant_id)
        print()
    
    if subnet_request_id:
        test_get_instruction_status(base_url, subnet_request_id, args.tenant_id)
        print()
    
    if eip_request_id:
        test_get_instruction_status(base_url, eip_request_id, args.tenant_id)
        print()
    
    print("Delete API testing completed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
