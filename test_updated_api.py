#!/usr/bin/env python3
"""
测试更新后的 API 接口，验证新的指令 ID 生成逻辑
"""

import requests
import json
import time
import hashlib

def generate_instruction_id(*params: str) -> str:
    """生成基于参数的 SHA-1 全局唯一 ID。"""
    combined_params = "|".join(params)
    sha1_hash = hashlib.sha1(combined_params.encode('utf-8')).hexdigest()
    return sha1_hash

def test_api_endpoints():
    """测试 API 端点"""
    base_url = "http://localhost:8080"
    
    print("=== 测试更新后的 API 接口 ===\n")
    
    # 测试参数
    tenant_id = "test-tenant-123"
    test_id = "test-001"
    vlan_id = 100
    eip = "************0"
    
    # 预期的指令 ID
    expected_test_id = generate_instruction_id(test_id)
    expected_gateway_id = generate_instruction_id(str(vlan_id))
    expected_eip_id = generate_instruction_id(str(vlan_id), eip)
    
    print(f"预期的指令 ID:")
    print(f"  Test: {expected_test_id}")
    print(f"  Gateway: {expected_gateway_id}")
    print(f"  EIP: {expected_eip_id}")
    print()
    
    # 1. 测试创建测试指令
    print("1. 测试创建测试指令")
    try:
        data = {
            "tenant_id": tenant_id,
            "test_id": test_id
        }
        response = requests.post(f"{base_url}/network/test", json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 创建成功: {result}")
            actual_test_id = result.get('instruction_id')
            print(f"  预期 ID: {expected_test_id}")
            print(f"  实际 ID: {actual_test_id}")
            print(f"  ID 匹配: {'✓' if actual_test_id == expected_test_id else '✗'}")
        else:
            print(f"✗ 创建失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")
    print()
    
    # 2. 测试创建子网网关
    print("2. 测试创建子网网关")
    try:
        data = {
            "tenant_id": tenant_id,
            "vlan_id": vlan_id,
            "subnet_gw_ip": "***********"
        }
        response = requests.post(f"{base_url}/network/subnet-gateway", json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 创建成功: {result}")
            actual_gateway_id = result.get('instruction_id')
            print(f"  预期 ID: {expected_gateway_id}")
            print(f"  实际 ID: {actual_gateway_id}")
            print(f"  ID 匹配: {'✓' if actual_gateway_id == expected_gateway_id else '✗'}")
        else:
            print(f"✗ 创建失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")
    print()
    
    # 3. 测试创建 EIP
    print("3. 测试创建 EIP")
    try:
        data = {
            "tenant_id": tenant_id,
            "vlan_id": vlan_id,
            "eip": eip,
            "gateway_ip": "***********",
            "internal_ip": "************"
        }
        response = requests.post(f"{base_url}/network/eip", json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 创建成功: {result}")
            actual_eip_id = result.get('instruction_id')
            print(f"  预期 ID: {expected_eip_id}")
            print(f"  实际 ID: {actual_eip_id}")
            print(f"  ID 匹配: {'✓' if actual_eip_id == expected_eip_id else '✗'}")
        else:
            print(f"✗ 创建失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 请求失败: {e}")
    print()
    
    # 4. 测试查询指令状态
    print("4. 测试查询指令状态")
    for name, instruction_id in [("Test", expected_test_id), ("Gateway", expected_gateway_id), ("EIP", expected_eip_id)]:
        try:
            response = requests.get(f"{base_url}/instruction/{instruction_id}?tenant_id={tenant_id}", timeout=5)
            if response.status_code == 200:
                result = response.json()
                print(f"✓ {name} 指令状态查询成功")
                instruction_data = result.get('instruction', {})
                print(f"  指令类型: {instruction_data.get('type')}")
                print(f"  指令 ID: {instruction_data.get('id')}")
            else:
                print(f"✗ {name} 指令状态查询失败: {response.status_code}")
        except Exception as e:
            print(f"✗ {name} 指令状态查询请求失败: {e}")
    print()
    
    # 5. 测试重复创建（应该返回相同的 ID）
    print("5. 测试重复创建")
    try:
        data = {
            "tenant_id": tenant_id,
            "test_id": test_id
        }
        response = requests.post(f"{base_url}/network/test", json=data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 重复创建测试指令: {result}")
            repeat_test_id = result.get('instruction_id')
            print(f"  第一次 ID: {expected_test_id}")
            print(f"  重复创建 ID: {repeat_test_id}")
            print(f"  ID 一致性: {'✓' if repeat_test_id == expected_test_id else '✗'}")
        else:
            print(f"✗ 重复创建失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"✗ 重复创建请求失败: {e}")
    print()
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_api_endpoints()
