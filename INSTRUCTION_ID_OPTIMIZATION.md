# 指令 ID 优化说明

## 概述

本次优化为 v-switch 系统中的指令（instruction）添加了基于特定参数的 SHA-1 计算的全局唯一 ID 字段，实现了更好的指令管理和去重功能。

## 主要变更

### 1. ID 生成逻辑

新增了 `_generate_instruction_id()` 方法，使用 SHA-1 哈希算法基于特定参数生成全局唯一的指令 ID：

```python
def _generate_instruction_id(self, *params: str) -> str:
    """生成基于参数的 SHA-1 全局唯一 ID。"""
    # 使用 "|" 分割参数
    combined_params = "|".join(params)
    # 计算 SHA-1 哈希
    sha1_hash = hashlib.sha1(combined_params.encode('utf-8')).hexdigest()
    return sha1_hash
```

### 2. 指令类型与 ID 生成规则

根据不同的指令类型，使用不同的参数组合生成 ID：

| 指令类型 | 参数组合 | 示例 |
|---------|---------|------|
| test_instruction | test_id | `generate_id("test123")` |
| subnet_gateway_instruction | vlan_id | `generate_id("100")` |
| eip_instruction | vlan_id + eip | `generate_id("100", "*************")` |
| delete_test | "delete_test" + test_id | `generate_id("delete_test", "test123")` |
| delete_gateway | "delete_gateway" + vlan_id | `generate_id("delete_gateway", "100")` |
| delete_eip | "delete_eip" + vlan_id + eip | `generate_id("delete_eip", "100", "*************")` |

### 3. 指令结构变更

所有指令现在都包含 `id` 字段：

```json
{
    "id": "7288edd0fc3ffcbe93a0cf06e3568e28521687bc",
    "type": "test",
    "tenant_id": "tenant123",
    "test_id": "test123",
    "shard_id": "1",
    "cmds": [...],
    "revocation": [...]
}
```

### 4. 存储机制优化

- **插入指令**：使用生成的 instruction_id 作为 ETCD 的 key
- **删除指令**：直接根据 instruction_id 删除，无需遍历查找
- **查询指令**：支持根据 instruction_id 直接查询

### 5. API 接口更新

#### 响应格式变更
```json
// 旧格式
{
    "success": true,
    "request_id": "uuid-string",
    "message": "..."
}

// 新格式
{
    "success": true,
    "instruction_id": "sha1-hash-string",
    "message": "..."
}
```

#### 端点路径更新
- `GET /instruction/{instruction_id}` - 查询指令状态
- `DELETE /instruction/{instruction_id}` - 删除指令

#### 删除测试接口更新
```json
// 删除测试现在需要 test_id
{
    "tenant_id": "tenant123",
    "test_id": "test123"
}
```

### 6. 新增便捷方法

为了方便根据业务参数查询和删除指令，新增了以下方法：

```python
# 根据业务参数查询指令状态
get_test_instruction_status(test_id, tenant_id)
get_subnet_gateway_instruction_status(vlan_id, tenant_id)
get_eip_instruction_status(vlan_id, eip, tenant_id)

# 根据业务参数删除指令
delete_test_instruction(test_id, tenant_id)
delete_subnet_gateway_instruction(vlan_id, tenant_id)
delete_eip_instruction(vlan_id, eip, tenant_id)
```

## 优势

### 1. 全局唯一性
- 基于 SHA-1 哈希确保 ID 的全局唯一性
- 相同参数始终生成相同 ID，支持幂等操作

### 2. 去重功能
- 自动检测重复指令，避免重复创建
- 提高系统稳定性和资源利用率

### 3. 高效查询
- 直接根据 ID 查询，无需遍历
- 提升查询性能

### 4. 简化删除
- 根据业务参数直接计算 ID 并删除
- 无需先查询再删除的两步操作

### 5. 可预测性
- ID 生成规则明确，便于调试和运维
- 支持离线计算预期的指令 ID

## 兼容性

- 保持了原有 API 接口的基本结构
- 仅将 `request_id` 字段名改为 `instruction_id`
- 删除测试接口增加了 `test_id` 参数要求

## 测试验证

提供了两个测试脚本：

1. `test_instruction_id.py` - 验证 ID 生成逻辑
2. `test_updated_api.py` - 验证 API 接口功能

## 示例

### 创建测试指令
```bash
curl -X POST http://localhost:8080/network/test \
  -H "Content-Type: application/json" \
  -d '{"tenant_id": "tenant123", "test_id": "test123"}'

# 响应
{
    "success": true,
    "instruction_id": "7288edd0fc3ffcbe93a0cf06e3568e28521687bc",
    "message": "Test creation request submitted"
}
```

### 查询指令状态
```bash
curl "http://localhost:8080/instruction/7288edd0fc3ffcbe93a0cf06e3568e28521687bc?tenant_id=tenant123"
```

### 删除指令
```bash
curl -X DELETE "http://localhost:8080/instruction/7288edd0fc3ffcbe93a0cf06e3568e28521687bc?tenant_id=tenant123"
```

## 总结

本次优化显著提升了指令管理的效率和可靠性，通过引入基于业务参数的确定性 ID 生成机制，实现了指令的去重、高效查询和简化删除，为系统的稳定运行提供了更好的保障。
